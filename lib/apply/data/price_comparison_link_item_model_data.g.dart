// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'price_comparison_link_item_model_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PriceComparisonLinkItemModel _$PriceComparisonLinkItemModelFromJson(
    Map<String, dynamic> json) {
  return PriceComparisonLinkItemModel()
    ..province = json['province'] as String?
    ..customerType = json['customerType'] as String?
    ..follow = json['follow'] as bool?
    ..detailUrl = json['detailUrl'] as String?;
}

Map<String, dynamic> _$PriceComparisonLinkItemModelToJson(
    PriceComparisonLinkItemModel instance) =>
    <String, dynamic>{
      'province': instance.province,
      'customerType': instance.customerType,
      'follow': instance.follow,
      'detailUrl': instance.detailUrl,
    };

