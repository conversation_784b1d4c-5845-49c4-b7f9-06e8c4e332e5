import 'package:XyyBeanSproutsFlutter/apply/data/apply_order_list_item_data.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/apply/data/apply_request_model_data.dart';

/// 选项项模型（用于下拉选择框的选项）
class ApplyOptionItem {
  final String name; // 选项名称
  final String value; // 选项值

  const ApplyOptionItem({required this.name, required this.value});
}

/// 订单行模型（表示一个可配置的表单项）
class ApplyOrderRowModel {
  String? value; // 当前选中的值（可为空）
  String? valueText; // 当前选中的值（可为空）
  String name; // 表单项名称
  String drawerTitle; // 抽屉标题
  List<ApplyOptionItem> options; // 选项列表
  bool? disable = false;
  bool? search = false;

  ApplyOrderRowModel(
      {this.value,
      this.valueText,
      this.name = '',
      this.drawerTitle = '',
      this.options = const [], // 默认空列表
      this.disable,
      this.search});
}

/// 订单信息模型（主业务逻辑）
class ApplyOrderInfoModel {
  List<ApplyOrderListItemData>? list; // 订单列表（可为空）
  ApplyOrderRowModel hasBeenReturned; // 是否已退回
  ApplyOrderRowModel refundType; // 退款类型
  ApplyOrderRowModel reasonForRefund; // 退款原因
  ApplyOrderRowModel customerType; // 客户类型
  ApplyOrderRowModel orderType; // 订单类型
  ApplyOrderRowModel businessProvinceRegion; // 业务省区
  ApplyOrderRowModel shippingProvinceRegion; // 发货省区
  String refundDescription; // 退款描述
  int isWholeOrder;

  ApplyOrderInfoModel(
      {this.list,
      ApplyOrderRowModel? hasBeenReturned,
      ApplyOrderRowModel? refundType,
      ApplyOrderRowModel? reasonForRefund,
      ApplyOrderRowModel? customerType,
      ApplyOrderRowModel? orderType,
      ApplyOrderRowModel? businessProvinceRegion,
      ApplyOrderRowModel? shippingProvinceRegion,
      this.refundDescription = '', // 非空字段默认值
      this.isWholeOrder = 0})
      : hasBeenReturned = hasBeenReturned ?? ApplyOrderRowModel(),
        refundType = refundType ?? ApplyOrderRowModel(),
        reasonForRefund = reasonForRefund ?? ApplyOrderRowModel(),
        customerType = customerType ?? ApplyOrderRowModel(),
        orderType = orderType ?? ApplyOrderRowModel(),
        businessProvinceRegion = businessProvinceRegion ?? ApplyOrderRowModel(),
        shippingProvinceRegion = shippingProvinceRegion ?? ApplyOrderRowModel();

  /// 初始化“是否已退回”选项
  void initHasBeenReturned() {
    hasBeenReturned = ApplyOrderRowModel(
      value: null,
      // 初始未选择
      valueText: null,
      name: "是否已退回",
      drawerTitle: "请选择是否已退回",
      options: const [
        ApplyOptionItem(name: "是", value: "1"),
        ApplyOptionItem(name: "否", value: "0"),
      ],
    );
  }

  /// 初始化“退款类型”选项
  void initRefundType() {
    refundType = ApplyOrderRowModel(
      value: null,
      valueText: null,
      name: "退款类型",
      drawerTitle: "请选择退款类型",
      options: const [
        ApplyOptionItem(name: "非破损非近效期", value: "-6700396257248063771"),
        ApplyOptionItem(name: "非近效期破损", value: "-5460415865475481309"),
        ApplyOptionItem(name: "近效期未破损", value: "-6697938055163327598"),
        ApplyOptionItem(name: "近效期且破损", value: "9045680371352617972"),
      ],
    );
  }

  /// 初始化“退款原因”选项
  void initReasonForRefund() {
    reasonForRefund = ApplyOrderRowModel(
      value: '1',
      valueText: '协商一致退款',
      name: "退款原因",
      drawerTitle: "请选择退款原因",
      options: const [
        ApplyOptionItem(name: "协商一致退款", value: "1"),
      ],
      disable: true,
    );
  }

  void initCustomerType() async {
    var result =
        await NetworkV2<ApplyCustomerInfoModel>(ApplyCustomerInfoModel())
            .requestDataV2(
      '/worries/customer_type',
      method: RequestMethod.GET,
      contentType: RequestContentType.FORM,
    );
    var optionList = result
            .getListData()
            ?.map((item) => ApplyOptionItem(
                  name: item.name ?? "",
                  value: item.code ?? "",
                ))
            .toList() ??
        [];

    customerType = ApplyOrderRowModel(
      value: null,
      valueText: null,
      name: "客户类型",
      drawerTitle: "请选择客户类型",
      options: optionList,
    );
  }

  void initOrderType() async {
    var result =
        await NetworkV2<ApplyCustomerInfoModel>(ApplyCustomerInfoModel())
            .requestDataV2(
      '/worries/order_type',
      method: RequestMethod.GET,
      contentType: RequestContentType.FORM,
    );
    var optionList = result
            .getListData()
            ?.map((item) => ApplyOptionItem(
                  name: item.name ?? "",
                  value: item.code ?? "",
                ))
            .toList() ??
        [];
    orderType = ApplyOrderRowModel(
      value: null,
      valueText: null,
      name: "订单类型",
      drawerTitle: "请选择订单类型",
      options: optionList,
    );
  }

  void initBusinessProvinceRegion() async {
    var result =
        await NetworkV2<ApplyCustomerInfoModel>(ApplyCustomerInfoModel())
            .requestDataV2(
      '/worries/businessArea',
      method: RequestMethod.GET,
      contentType: RequestContentType.FORM,
    );
    var optionList = result
            .getListData()
            ?.map((item) => ApplyOptionItem(
                  name: item.name ?? "",
                  value: item.name ?? "",
                ))
            .toList() ??
        [];
    businessProvinceRegion = ApplyOrderRowModel(
        value: null,
        valueText: null,
        name: "业务省区",
        drawerTitle: "请选择业务省区",
        options: optionList,
        search: true);
  }

  void initShippingProvinceRegion() async {
    var result =
        await NetworkV2<ApplyCustomerInfoModel>(ApplyCustomerInfoModel())
            .requestDataV2(
      '/worries/allArea',
      method: RequestMethod.GET,
      contentType: RequestContentType.FORM,
    );
    var optionList = result
            .getListData()
            ?.map((item) => ApplyOptionItem(
                  name: item.name ?? "",
                  value: item.name ?? "",
                ))
            .toList() ??
        [];
    shippingProvinceRegion = ApplyOrderRowModel(
      value: null,
      valueText: null,
      name: "发货省区",
      drawerTitle: "请选择发货省区",
      options: optionList,
    );
  }
}

//定义订单选择的返回值
class ReturnSelectInfoData {
  final List<ApplyOrderListItemData>? returnList;
  final String? keyword;
  final int? isWholeOrder;

  ReturnSelectInfoData({this.returnList, this.keyword, this.isWholeOrder});
}
