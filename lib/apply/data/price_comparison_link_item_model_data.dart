import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'price_comparison_link_item_model_data.g.dart';

//获取用户基本信息
@JsonSerializable()
class PriceComparisonLinkItemModel
    extends BaseModelV2<PriceComparisonLinkItemModel> {
  String? province; //省份
  String? customerType; //客户类型
  bool? follow; //是否关注
  String? detailUrl; //详情页链接

  PriceComparisonLinkItemModel();


  @override
  PriceComparisonLinkItemModel fromJsonMap(Map<String, dynamic> json) {
    return _$PriceComparisonLinkItemModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$PriceComparisonLinkItemModelToJson(this);
  }
}
