import 'dart:convert';
import 'dart:io';
import 'package:XYYContainer/XYYContainer.dart';
import 'package:flutter/foundation.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/apply/data/price_comparison_link_item_model_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

class PriceComparisonLink extends BasePage {
  PriceComparisonLink();

  @override
  BaseState initState() {
    return _PriceComparisonLinkState();
  }
}

class _PriceComparisonLinkState extends BaseState {
  _PriceComparisonLinkState();
  ///列表数据
  List<PriceComparisonLinkItemModel> listData = [];
  @override
  void initState() {
    super.initState();
    _initialize();
  }

  void _initialize() {
    // 初始化逻辑
    this._requestItemData();
  }

  void _requestItemData() async {
    var result = await NetworkV2<PriceComparisonLinkItemModel>(PriceComparisonLinkItemModel())
        .requestDataV2(
        'compare/listArea',
        contentType: RequestContentType.FORM,
        method: RequestMethod.GET,
    );
    dismissLoadingDialog();
    if (mounted) {
      if (result.isSuccess == true) {
        var data = result.getListData();
        print('xyyyz,${json.encode(data)}');
        this.listData = data ?? [];
        setState(() {});
      }
    }
  }

  void openCommonH5Url(String jumpUrl) {
    jumpUrl = Uri.encodeComponent(jumpUrl);
    String router = "xyy://crm-app.ybm100.com/crm/web_view?url=$jumpUrl";
    XYYContainer.open(router);
  }

  @override
  Widget buildWidget(BuildContext context) {
    return SingleChildScrollView(
        child: Container(
          color: Color(0xFFF7F7F8),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: listData != null ? listData.map((item) {
              return Container(
                margin: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      width: 150,
                      child: Text(
                        '${item.province}-${item.customerType}',
                        style: TextStyle(fontSize: 14),
                        softWrap: true, // 允许文本换行
                      ),
                    ),
                    SizedBox(width: 4),
                    ElevatedButton(
                      onPressed: () {
                        String urlToCopy = item.detailUrl ?? '';
                        if (!urlToCopy.startsWith('http://') && !urlToCopy.startsWith('https://')) {
                            var urlLink = kReleaseMode ? 'https://crm.ybm100.com' : 'https://crm.test.ybm100.com';
                            urlToCopy = urlLink + '$urlToCopy';
                            print('urlurl${urlToCopy}');
                        }
                        Clipboard.setData(ClipboardData(text: urlToCopy));
                        XYYContainer.toastChannel.toast('已复制链接');
                      },
                      style: ButtonStyle(
                        backgroundColor: MaterialStateProperty.all(Colors.green),
                        padding: MaterialStateProperty.all(EdgeInsets.symmetric(horizontal: 8, vertical: 4)),
                        minimumSize: MaterialStateProperty.all(Size(60, 30)),
                      ),
                      child: Text('复制链接', style: TextStyle(fontSize: 12)),
                    ),
                    SizedBox(width: 4),
                    ElevatedButton(
                      onPressed: () {
                        // 查看逻辑
                        EasyLoading.show();
                        String urlToCopy = item.detailUrl ?? '';
                        if (!urlToCopy.startsWith('http://') && !urlToCopy.startsWith('https://')) {
                          var urlLink = kReleaseMode ? 'https://crm.ybm100.com' : 'https://crm.test.ybm100.com';
                          urlToCopy = urlLink + '$urlToCopy';
                          print('urlurl1${urlToCopy}');
                        }
                        openCommonH5Url(urlToCopy);
                        EasyLoading.dismiss();
                      },
                      style: ButtonStyle(
                        backgroundColor: MaterialStateProperty.all(Colors.green),
                        padding: MaterialStateProperty.all(EdgeInsets.symmetric(horizontal: 8, vertical: 4)),
                        minimumSize: MaterialStateProperty.all(Size(40, 30)),
                      ),
                      child: Text('查看', style: TextStyle(fontSize: 12)),
                    ),
                    SizedBox(width: 4),
                    ElevatedButton(
                      onPressed: () async {
                        print('qiyuqiyu1${item.follow}');
                        // 切换关注状态
                        item.follow = item.follow == true ? false : true;
                        var sysUserId = await UserInfoUtil.sysUserId();
                        print('qiyuqiyu${item.follow},${item}');
                        EasyLoading.show();
                        // 只传递必要的参数
                        var parameters = {
                          'province': item.province,
                          'customerType': item.customerType,
                          'follow': item.follow,
                          'sysUserId': sysUserId,
                        };
                        var result = await NetworkV2<PriceComparisonLinkItemModel>(PriceComparisonLinkItemModel())
                            .requestDataV2('compare/follow',
                            parameters: parameters,
                            method: RequestMethod.POST,
                            contentType: RequestContentType.JSON
                        );
                        EasyLoading.dismiss();
                        if (result.isSuccess == true) {
                          print('qiyuqiyu2');
                          this._requestItemData();
                        }
                      },
                      child: Text(item.follow == true ?'取消关注' : '关注', style: TextStyle(fontSize: 12)),
                      style: ButtonStyle(
                        backgroundColor: item.follow == true ? MaterialStateProperty.all(Colors.blue) : MaterialStateProperty.all(Colors.green),
                        padding: MaterialStateProperty.all(EdgeInsets.symmetric(horizontal: 8, vertical: 4)),
                        minimumSize: MaterialStateProperty.all(Size(50, 30)),
                      ),
                    ),
                  ],
                ),
              );
            }).toList() : [],
          ),
        ));
  }

  @override
  String getTitleName() {
    return "比价链接";
  }

}
