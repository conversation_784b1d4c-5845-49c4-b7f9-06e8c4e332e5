import 'dart:convert';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/event_bus/event_bus.dart';
import 'package:XyyBeanSproutsFlutter/common/tabs/custom_tab_controller.dart';
import 'package:XyyBeanSproutsFlutter/common/tabs/custom_tabs.dart';
import 'package:XyyBeanSproutsFlutter/customer/page/customer_tab_page.dart';
import 'package:XyyBeanSproutsFlutter/goods/goods_page.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/home_page.dart';
import 'package:XyyBeanSproutsFlutter/main/data/user_auth_model.dart';
import 'package:XyyBeanSproutsFlutter/main/page/center_quick_access_page.dart';
import 'package:XyyBeanSproutsFlutter/mine/page/mine_page.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';

import 'package:XyyBeanSproutsFlutter/utils/permission/permission_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:flutter/services.dart';

class MainTabPage extends BasePage {
  @override
  BaseState<StatefulWidget> initState() {
    return MainTabPageState();
  }
}

class MainTabPageState extends BaseState<MainTabPage>
    with SingleTickerProviderStateMixin, EventBusObserver {
  int selectedIndex = 0;

  late CustomTabController _controller;

  DateTime? lastPopTime;

  int customerExtraType = 0;

  @override
  void initState() {
    
    this._controller = CustomTabController(
      length: 5,
      vsync: this,
      animationDuration: Duration.zero,
    );

    /// 订阅切换tab的通知
    this.eventBus.addListener(
        observer: this,
        eventName: MainTabBarEventBusName.CHANGE_TAB_INDEX,
        callback: (index) {
          this._controller.index = index;
          this.selectedIndex = index;
          setState(() {});
        });

    this.eventBus.addListener(
        observer: this,
        eventName: MainTabBarEventBusName.CHANGE_FILTER_POI_REGISTER,
        callback: (index) {
          customerExtraType = index;
        });

    this.requestUserAuth();
    super.initState();
    eventBus.addListener(
        observer: this,
        eventName: MainTabBarEventBusName.OPEN_MAIN,
        callback: _openMainCallback);
    permissionInfo();
  }

  void _openMainCallback(dynamic arg) {
    if (arg is int) {
      Navigator.of(context).popUntil((route) => route.settings.name == "/main");
      _controller.index = arg;
    }
  }

  @override
  void dispose() {
    this.eventBus.removeListener(observer: this);
    this._controller.dispose();
    super.dispose();
  }

  @override
  Widget buildWidget(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
      systemNavigationBarColor: Colors.white,
    ));
    return WillPopScope(
      onWillPop: () async {
        if (lastPopTime == null ||
            DateTime.now().difference(lastPopTime!) > Duration(seconds: 1)) {
          lastPopTime = DateTime.now();

          showToast("再按一次退出");
          return Future.value(false);
        } else {
          lastPopTime = DateTime.now();
          // 退出app
          return Future.value(true);
        }
      },
      child: Scaffold(
        backgroundColor: Color(0xFFFFFFFF),
        bottomNavigationBar: SafeArea(
          child: Container(
            color: Color(0xFFFFFFFF),
            child: SizedBox(
              height: 49,
              child: CustomTabBar(
                controller: this._controller,
                isScrollable: false,
                indicatorColor: Colors.transparent,
                indicatorWeight: 0.1,
                // 指示器高度不能设置为0
                indicatorSize: CustomTabBarIndicatorSize.label,
                unselectedLabelColor: Color(0xFF9494A6),
                unselectedLabelStyle: TextStyle(fontSize: 10),
                labelColor: Color(0xFF333333),
                labelStyle: TextStyle(fontSize: 10),
                tabs: this.getTabs(),
                disableClickTabIndex: [2],
                onTap: (index) {
                  if(index == 1){
                    print('jinru');
                    eventBus.sendMessage(
                      QT.qtYBMCustomerPublicPage,
                    );
                  }
                  this.selectedIndex = index;
                  setState(() {});
                },
              ),
            ),
          ),
        ),
        body: CustomTabBarView(
          physics: new NeverScrollableScrollPhysics(),
          controller: this._controller,
          children: [
            Container(child: HomePage()),
            Container(child: CustomerTabPage(customerExtraType: customerExtraType,)),
            Container(),
            Container(
              child: GoodsPage(),
            ),
            Container(child: MinePage()),
          ],
        ),
      ),
    );
  }

  List<Widget> getTabs() {
    return [
      Tab(
        icon: ImageIcon(
          AssetImage(this.selectedIndex == 0
              ? 'assets/images/tab/tab_home_selected.png'
              : 'assets/images/tab/tab_home_normal.png'),
          size: 22,
        ),
        iconMargin: EdgeInsets.only(bottom: 1),
        text: "首页",
      ),
      Tab(
        icon: ImageIcon(
          AssetImage(this.selectedIndex == 1
              ? 'assets/images/tab/tab_customer_selected.png'
              : 'assets/images/tab/tab_customer_normal.png'),
          size: 22,
        ),
        iconMargin: EdgeInsets.only(bottom: 1),
        text: "客户",
      ),
      GestureDetector(
        onTap: () {
          showQuickAccess(context);
        },
        child: Image.asset(
          'assets/images/tab/tab_center_add.png',
          width: 36,
          height: 36,
        ),
      ),
      Tab(
        icon: ImageIcon(
          AssetImage(this.selectedIndex == 3
              ? 'assets/images/tab/tab_goods_selected.png'
              : 'assets/images/tab/tab_goods_normal.png'),
          size: 22,
        ),
        iconMargin: EdgeInsets.only(bottom: 1),
        text: "商品",
      ),
      Tab(
        icon: ImageIcon(
          AssetImage(this.selectedIndex == 4
              ? 'assets/images/tab/tab_mine_selected.png'
              : 'assets/images/tab/tab_mine_normal.png'),
          size: 22,
        ),
        iconMargin: EdgeInsets.only(bottom: 1),
        text: "我的",
      ),
    ];
  }

  // Request
  // 用户身份 (hy, ybm)
  void requestUserAuth() async {
    var result = await NetworkV2<UserAuthModel>(UserAuthModel())
        .requestDataV2("lz/getRoles");
    if (result.isSuccess == true) {
      // print(result.getListData());
      List<UserAuthModel> data = result.getListData() ?? [];
      List<dynamic> list = data.map((e) => e.toJson()).toList();
      String roleJSON = jsonEncode(list);
      XYYContainer.storageChannel.put('roleJSON', roleJSON);
    }
  }

void permissionInfo() async {
  var userInfo = await UserInfoUtil.getUserInfo();
  var result =
        await NetworkV2<PermissionData>(PermissionData())
            .requestDataV2(
      '/permission/getPermission',
      parameters: userInfo?.sysUserId,
      method: RequestMethod.GET,
      contentType: RequestContentType.FORM,
    );
    if (result.isSuccess == true) {
        PermissionData? data = result.getData();
        Permission.list = data?.permissionList ?? [];
    }
  

}
  @override
  String getTitleName() {
    return "";
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return null;
  }
}
