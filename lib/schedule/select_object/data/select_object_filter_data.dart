import 'package:XyyBeanSproutsFlutter/common/filter/common_filter_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'select_object_filter_data.g.dart';

@JsonSerializable()
class SelectObjectFilertRootModel extends BaseModelV2 {
  List<SelectObjectFilterData>? customerStatus;
  List<SelectObjectFilterData>? lifeCycle;
  List<SelectObjectFilterData>? orderCondition;
  List<SelectObjectFilterData>? licensePrepare;
  List<SelectObjectFilterData>? customerType;
  List<SelectObjectFilterData>? saasUserFlag;
  List<SelectObjectFilterData>? levelType;
  List<SelectObjectFilterData>? visitCondition;
  /// 添加计划 -- 动销
  List<SelectObjectFilterData>? placeOrderCondition;
  /// 添加计划 -- 等级
  List<SelectObjectFilterData>? levelCondition;
  /// 添加计划 -- 距离
  List<SelectObjectFilterData>? distanceCondition;
  /// 添加计划 -- 资质
  List<SelectObjectFilterData>? licenseStatusCondition;

  SelectObjectFilertRootModel();

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return SelectObjectFilertRootModel.fromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$SelectObjectFilertRootModelToJson(this);
  }

  factory SelectObjectFilertRootModel.fromJson(Map<String, dynamic> json) =>
      _$SelectObjectFilertRootModelFromJson(json);
}

@JsonSerializable()
class SelectObjectFilterData extends BaseModelV2 {
  dynamic? selected;
  dynamic? text;
  dynamic? code;

  SelectObjectFilterData();

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return SelectObjectFilterData.fromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$SelectObjectFilterDataToJson(this);
  }

  factory SelectObjectFilterData.fromJson(Map<String, dynamic> json) =>
      _$SelectObjectFilterDataFromJson(json);
}

class SelectObjectFilterConfigModel {
  String itemKey;

  String itemTitle;

  List<CommonFilterItemEntry> contents;

  /// 默认选中
  String? defaultId;

  /// 是否支持多选
  bool isAllowMultipleSelection;

  /// 是否支持多选情况下与默认选中互斥
  bool isMutexByDefault;

  /// 是否可不选中
  bool isAllowClean;

  SelectObjectFilterConfigModel({
    required this.itemKey,
    required this.itemTitle,
    this.contents = const [],
    this.defaultId,
    this.isAllowMultipleSelection = false,
    this.isMutexByDefault = false,
    this.isAllowClean = false,
  });
}

class ObjectFilterKey {
  static String groupKeyName = "groupId";

  /// 区域编码 2.9.0
  static String areaKeyName = "areaCode";

  /// 客户状态 2.9.0
  static String statusKeyName = "customerStatusCode";

  /// 客户等级 6.5.0
  static String levelKeyName = "customerLevelCode";

  /// 拜访情况 6.5.0
  static String visitKeyName = "visitCondition";

  /// 下单情况 2.9.0
  static String orderKeyName = "orderConditionCode";

  /// 生命周期2.9.0
  static String lifeKeyName = "lifeCycleCode";

  /// 客户类型 2.9.0
  static String typeKeyName = "customerTypeCode";

  ///智慧脸客户
  static String saasUserFlag = "saasUserFlag";

  /// 是否可用医保
  static String medicalKeyName = "medicalInsurance";

  /// 周边环境
  static String envKeyName = "aroundEnvArray";

  /// 需求SKU数
  static String skuCountKeyName = "buySkusArray";

  /// 是否需要动销
  static String needSalesKeyName = "needPullSales";

  /// 是否需要门店诊断
  static String needDiagnoseKeyName = "needMerchantDiagnose";

  /// 是否需要店员培训
  static String needTrainsKeyName = "needClerkTrains";

  /// 商户资质状态
  static String licenseKeyName = "licenseStatuses";

  /// 添加计划 -- 资质
  static String licenseCodeKeyName = "licenseCode";

  /// 添加计划 -- 距离范围
  static String distanceKeyName = "distance";
}
