// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'select_object_filter_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SelectObjectFilertRootModel _$SelectObjectFilertRootModelFromJson(
    Map<String, dynamic> json) {
  return SelectObjectFilertRootModel()
    ..customerStatus = (json['customerStatus'] as List<dynamic>?)
        ?.map((e) => SelectObjectFilterData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..lifeCycle = (json['lifeCycle'] as List<dynamic>?)
        ?.map((e) => SelectObjectFilterData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..orderCondition = (json['orderCondition'] as List<dynamic>?)
        ?.map((e) => SelectObjectFilterData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..licensePrepare = (json['licensePrepare'] as List<dynamic>?)
        ?.map((e) => SelectObjectFilterData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..customerType = (json['customerType'] as List<dynamic>?)
        ?.map((e) => SelectObjectFilterData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..saasUserFlag = (json['saasUserFlag'] as List<dynamic>?)
        ?.map((e) => SelectObjectFilterData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..levelType = (json['levelType'] as List<dynamic>?)
        ?.map((e) => SelectObjectFilterData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..visitCondition = (json['visitCondition'] as List<dynamic>?)
        ?.map((e) => SelectObjectFilterData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..placeOrderCondition = (json['placeOrderCondition'] as List<dynamic>?)
        ?.map((e) => SelectObjectFilterData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..levelCondition = (json['levelCondition'] as List<dynamic>?)
        ?.map((e) => SelectObjectFilterData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..distanceCondition = (json['distanceCondition'] as List<dynamic>?)
        ?.map((e) => SelectObjectFilterData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..licenseStatusCondition = (json['licenseStatusCondition']
            as List<dynamic>?)
        ?.map((e) => SelectObjectFilterData.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$SelectObjectFilertRootModelToJson(
        SelectObjectFilertRootModel instance) =>
    <String, dynamic>{
      'customerStatus': instance.customerStatus,
      'lifeCycle': instance.lifeCycle,
      'orderCondition': instance.orderCondition,
      'licensePrepare': instance.licensePrepare,
      'customerType': instance.customerType,
      'saasUserFlag': instance.saasUserFlag,
      'levelType': instance.levelType,
      'visitCondition': instance.visitCondition,
      'placeOrderCondition': instance.placeOrderCondition,
      'levelCondition': instance.levelCondition,
      'distanceCondition': instance.distanceCondition,
      'licenseStatusCondition': instance.licenseStatusCondition,
    };

SelectObjectFilterData _$SelectObjectFilterDataFromJson(
    Map<String, dynamic> json) {
  return SelectObjectFilterData()
    ..selected = json['selected']
    ..text = json['text']
    ..code = json['code'];
}

Map<String, dynamic> _$SelectObjectFilterDataToJson(
        SelectObjectFilterData instance) =>
    <String, dynamic>{
      'selected': instance.selected,
      'text': instance.text,
      'code': instance.code,
    };
