import 'dart:collection';
import 'dart:convert';
import 'dart:io';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/filter/common_filter_item.dart';
import 'package:XyyBeanSproutsFlutter/schedule/select_object/data/select_object_area_data.dart';
import 'package:XyyBeanSproutsFlutter/schedule/select_object/data/select_object_filter_data.dart';
import 'package:XyyBeanSproutsFlutter/schedule/select_object/widget/select_object_multiple_item.dart';
import 'package:XyyBeanSproutsFlutter/schedule/select_object/widget/select_object_select_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

class ObjectFilterContentPage extends StatefulWidget {
  final Map<String, String> filterParams;
  final Map<String, dynamic> cacheParams;
  final bool isMapFilter;

  ObjectFilterContentPage({
    this.filterParams = const {},
    this.cacheParams = const {},
    this.isMapFilter = false,
  });

  @override
  @override
  State<StatefulWidget> createState() {
    return ObjectFilterContentPageState();
  }
}

class ObjectFilterContentPageState extends State<ObjectFilterContentPage> {
  /// 数据源
  List<SelectObjectFilterConfigModel> dataSource = [];

  /// 用来保存提示按钮
  GlobalKey _anchorKey = GlobalKey();

  /// 当前选择的参数
  Map<String, String> filterMap = {};

  /// 当前页面上展示用的缓存数据
  Map<String, dynamic> cacheMap = {};

  @override
  void initState() {
    this.filterMap = widget.filterParams;
    this.cacheMap = widget.cacheParams;

    this.requestFilterData();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding:
          EdgeInsets.only(bottom: MediaQuery.of(context).viewPadding.bottom),
      color: Color(0xFFFFFFFF),
      child: Column(
        children: [
          this.backBar(),
          Expanded(
            child: ListView.builder(
              cacheExtent: 99999,
              padding: EdgeInsets.zero,
              itemCount: this.dataSource.length,
              itemBuilder: this.builderItem,
            ),
          ),
          this.bottomWidget(),
        ],
      ),
    );
  }

  Widget backBar() {
    return widget.isMapFilter
        ? Container(
            child: Row(
              children: [
                Spacer(),
                Container(
                  child: IconButton(
                    icon: ImageIcon(
                      AssetImage('assets/images/titlebar/icon_close.png'),
                      color: Colors.black,
                      size: 22,
                    ),
                    onPressed: () {
                      print("不响应事件");
                      Navigator.of(context).pop();
                    },
                    highlightColor: Colors.transparent,
                    splashColor: Colors.transparent,
                  ),
                )
              ],
            ),
          )
        : Container();
  }

  /// 构建item
  Widget builderItem(BuildContext context, int index) {
    SelectObjectFilterConfigModel config = this.dataSource[index];
    if (this.isSelectItem(config)) {
      return Container(
        padding: EdgeInsets.only(left: 10, right: 10),
        child: SelectObjectSelectItem(
          title: config.itemTitle,
          itemKey: config.itemKey,
          content: this.getSelectContent(config.itemKey),
          selectAction: (itemKey) {
            this.selectAction(itemKey);
          },
        ),
      );
    }
    if (config.isAllowMultipleSelection) {
      if (config.itemKey == ObjectFilterKey.lifeKeyName) {
        return Container(
          padding: EdgeInsets.all(10),
          child: SelectObjectFilterItem.multupleTips(
            title: config.itemTitle,
            contents: config.contents,
            defaultId: config.defaultId,
            selectedIds: this.getSelectIdsForKey(config.itemKey),
            isAllowMultipleSelection: config.isAllowMultipleSelection,
            isMutexByDefault: config.isMutexByDefault,
            isAllowClean: config.isAllowClean,
            tips: IconButton(
              key: this._anchorKey,
              alignment: Alignment.centerLeft,
              icon: Image.asset(
                'assets/images/base/select_object_tips_icon.png',
                width: 15,
                height: 15,
              ),
              constraints: BoxConstraints(maxHeight: 18),
              iconSize: 30,
              padding: EdgeInsets.zero,
              onPressed: () {
                this._salesTipsAction(context);
              },
            ),
            valueChange: (itemKey, selectedIds) {
              this.filterMap[itemKey] = selectedIds.join(",");
            },
            itemKey: config.itemKey,
          ),
        );
      }
      return Container(
        padding: EdgeInsets.all(10),
        child: SelectObjectFilterItem.multuple(
          title: config.itemTitle,
          contents: config.contents,
          defaultId: config.defaultId,
          selectedIds: this.getSelectIdsForKey(config.itemKey),
          isAllowMultipleSelection: config.isAllowMultipleSelection,
          isMutexByDefault: config.isMutexByDefault,
          isAllowClean: config.isAllowClean,
          valueChange: (itemKey, selectedIds) {
            this.filterMap[itemKey] = selectedIds.join(",");
          },
          itemKey: config.itemKey,
        ),
      );
    } else {
      return Container(
        padding: EdgeInsets.all(10),
        child: SelectObjectFilterItem.single(
          title: config.itemTitle,
          contents: config.contents,
          defaultId: config.defaultId,
          selectedIds: this.getSelectIdsForKey(config.itemKey),
          isAllowMultipleSelection: config.isAllowMultipleSelection,
          isMutexByDefault: config.isMutexByDefault,
          isAllowClean: config.isAllowClean,
          valueChange: (itemKey, selectIds) {
            this.filterMap[itemKey] = selectIds.join(",");
          },
          itemKey: config.itemKey,
        ),
      );
    }
  }

  /// 构建底部按钮
  Widget bottomWidget() {
    return Container(
      padding: EdgeInsets.fromLTRB(15, 15, 15, 5),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: Color(0xFFD3D3D3),
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(5),
              ),
              child: TextButton(
                onPressed: resetAllItem,
                style: ButtonStyle(
                  overlayColor:
                      MaterialStateProperty.all<Color>(Colors.transparent),
                  padding:
                      MaterialStateProperty.all<EdgeInsets>(EdgeInsets.zero),
                  minimumSize: MaterialStateProperty.all<Size>(Size.zero),
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                child: Container(
                  height: 40,
                  alignment: Alignment.center,
                  child: Text(
                    '重置',
                    style: TextStyle(
                      color: Color(0xFF333333),
                      fontSize: 16,
                    ),
                  ),
                ),
              ),
            ),
          ),
          SizedBox(width: 10),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Color(0xFF35C561),
                borderRadius: BorderRadius.circular(5),
              ),
              child: TextButton(
                onPressed: determineParams,
                child: Container(
                  height: 40,
                  alignment: Alignment.center,
                  child: Text(
                    '确定',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                style: ButtonStyle(
                  overlayColor:
                      MaterialStateProperty.all<Color>(Colors.transparent),
                  padding:
                      MaterialStateProperty.all<EdgeInsets>(EdgeInsets.zero),
                  minimumSize: MaterialStateProperty.all<Size>(Size.zero),
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
              ),
            ),
          )
        ],
      ),
    );
  }

  /// Events
  void resetAllItem() {
    this.cacheMap = {};
    this.filterMap = {};
    setState(() {});
  }

  void determineParams() {
    trackOrderAndLevelEvent();
    Map<String, dynamic> result = {
      "filterParams": this.filterMap,
      "cacheParams": this.cacheMap,
    };
    print('----bug 筛选：${result}');
    Navigator.of(context).pop(result);
  }

  void trackOrderAndLevelEvent() {
    if (filterMap.containsKey(ObjectFilterKey.orderKeyName)) {
      switch (filterMap[ObjectFilterKey.orderKeyName]?.toString()) {
        case "1": // 本月未动销
          track(widget.isMapFilter ? "mc-map-unorder" : "mc-customer-unorder");
          break;
        case "2": // 本月已动销
          track(widget.isMapFilter ? "mc-map-haorder" : "mc-customer-haorder");
          break;
        case "3": // 从未下单
          track(widget.isMapFilter
              ? "mc-map-neverorder"
              : "mc-customer-neverorder");
          break;
      }
    }

    if (filterMap.containsKey(ObjectFilterKey.levelKeyName)) {
      switch (filterMap[ObjectFilterKey.levelKeyName]?.toString()) {
        case "1":
          track("mc-map-gradeS"); // S
          break;
        case "5":
          track("mc-map-gradeA"); // A
          break;
        case "9":
          track("mc-map-gradeB"); // B
          break;
        case "13":
          track("mc-map-gradeC"); // C
          break;
        case "14":
          track("mc-map-gradeD"); // D
          break;
      }
    }
  }

  /// 选择事件
  void selectAction(String itemKey) {
    if (itemKey == ObjectFilterKey.groupKeyName) {
      this.selectGroupAction();
    }
    if (itemKey == ObjectFilterKey.areaKeyName) {
      this.selectAreaAction();
    }
  }

  /// 选择范围事件
  void selectGroupAction() {
    track(widget.isMapFilter ? "mc-map-selectid" : "mc-customer-selectid");
    XYYContainer.open(
        'xyy://crm-app.ybm100.com/executor?needSetResult=true&canChooseDepartment=true',
        callback: (params) {
      if (params != null && mounted) {
        setState(() {
          this.cacheMap.addAll(params);
          if (params.keys.contains("id")) {
            if (params["isgroup"] == 'true') {
              String groupId = params["id"].toString();
              this.filterMap.remove("searchUserId");
              if (groupId.length > 0) {
                this.filterMap['groupId'] = groupId;
              } else {
                this.filterMap.remove("groupId");
              }
            } else {
              this.filterMap.remove("groupId");
              this.filterMap['searchUserId'] = params["id"];
            }
            this.cacheMap["name"] = params["name"];
          } else {
            this.cacheMap.remove("name");
            this.filterMap.remove("groupId");
            this.filterMap.remove("searchUserId");
          }
        });
      }
    });
  }

  void track(String actionType, {Map<String, String>? extras}) {
    var hashMap = HashMap<String, String>();
    hashMap['action_type'] = actionType;
    if (extras != null && extras.isNotEmpty) {
      hashMap.addAll(extras);
    }
    XYYContainer.bridgeCall('event_track', parameters: hashMap);
  }

  /// 选择区域事件
  void selectAreaAction() {
    track(widget.isMapFilter ? "mc-map-region" : "mc-customer-region");
    String router;
    if (Platform.isAndroid) {
      router = 'xyy://crm-app.ybm100.com/select_area_v2?';
    } else {
      router = 'xyy://crm-app.ybm100.com/select_area?';
    }
    if (this.cacheMap.keys.contains("parentParam")) {
      router +=
          "parentParam=" + json.encode(this.cacheMap["parentParam"]) + "&";
    }
    if (this.cacheMap.keys.contains("areaParam")) {
      router += "areaParam=" + json.encode(this.cacheMap["areaParam"]);
    }
    router = Uri.encodeFull(router);
    XYYContainer.open(router, callback: (params) {
      setState(() {
        if (params != null) {
          // 兼容Android container返回值处理
          if (params.keys.length == 0 ||
              (Platform.isAndroid && params.containsKey("reset"))) {
            this.cacheMap.remove("parentParam");
            this.cacheMap.remove("areaParam");
            this.filterMap.remove("areaCode");
            this.filterMap.remove("areaCodeLevel");
          } else {
            this.cacheMap.addAll(params);

            SelectObjectAreaCallBackModel backModel =
                SelectObjectAreaCallBackModel.fromJsonMap(params);
            if (backModel.areaParam?.length == 1) {
              // 只有一个区或没有
              SelectObjectAreaModel? areaModel = backModel.areaParam?.first;
              if (areaModel?.areaLevel != "") {
                // 只选择一个区
                this.filterMap["areaCode"] = areaModel?.areaCode ?? "";
                this.filterMap["areaCodeLevel"] = areaModel?.areaLevel ?? "";
              } else {
                // 没有选择区，判断市
                SelectObjectAreaModel? parentModel =
                    backModel.parentParam?.last;
                if (parentModel?.areaLevel != "") {
                  this.filterMap["areaCode"] = parentModel?.areaCode ?? "";
                  this.filterMap["areaCodeLevel"] =
                      parentModel?.areaLevel ?? "";
                } else {
                  //没有选择区，判断省
                  this.filterMap["areaCode"] =
                      backModel.parentParam?.first.areaCode ?? "";
                  this.filterMap["areaCodeLevel"] =
                      backModel.parentParam?.first.areaLevel ?? "";
                }
              }
            } else {
              var code = backModel.areaParam?.map((e) => e.areaCode).join(',');
              var level =
                  backModel.parentParam?.map((e) => e.areaLevel).join(',');
              this.filterMap["areaCode"] = code ?? "";
              this.filterMap["areaCodeLevel"] = level ?? "";
            }
          }
        }
      });
    });
  }

  /// 获取选择得内容
  String getSelectContent(String itemKey) {
    if (itemKey == ObjectFilterKey.groupKeyName) {
      return this.getGroupName();
    }
    if (itemKey == ObjectFilterKey.areaKeyName) {
      return this.getAreaName();
    }
    return "全部";
  }

  /// 获取选择的范围名称
  String getGroupName() {
    if (this.cacheMap.keys.contains("name")) {
      return this.cacheMap["name"];
    }
    return "全部";
  }

  /// 获取选择的区域名称
  String getAreaName() {
    if (this.cacheMap.containsKey("areaParam")) {
      return (this.cacheMap["areaParam"] as List<dynamic>)
          .map((e) =>
              (json.decode(e) as Map<String, dynamic>)["areaName"].toString())
          .join(",");
    }
    return "全部";
  }

  /// 生命周期提示提示按钮点击事件
  void _salesTipsAction(BuildContext context) {
    // XYYContainer.toastChannel.toast('点击了提示');
    dynamic renderBox = this._anchorKey.currentContext?.findRenderObject()!;
    Offset offset =
        renderBox.localToGlobal(Offset(10.0, renderBox.size.height));
    showGeneralDialog(
      context: context,
      barrierColor: Colors.transparent,
      // 遮罩颜色
      barrierLabel: "",
      barrierDismissible: true,
      // 点击遮罩是否关闭对话框
      transitionDuration: const Duration(milliseconds: 200),
      // 对话框打开/关闭的动画时长
      pageBuilder: (
        // 构建对话框内部UI
        BuildContext context,
        Animation animation,
        Animation secondaryAnimation,
      ) {
        return Stack(
          children: [
            Positioned(
              child: SelectObjectTipsPopover(),
              top: offset.dy,
              left: offset.dx - 77.5,
            )
          ],
        );
      },
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(
          opacity: CurvedAnimation(
            parent: animation,
            curve: Curves.easeOut,
          ),
          child: child,
        );
      },
    );
  }

  /// Request
  void requestFilterData() async {
    EasyLoading.show(maskType: EasyLoadingMaskType.clear);
    var result = await NetworkV2<SelectObjectFilertRootModel>(
            SelectObjectFilertRootModel())
        .requestDataV2('customerV2/other/condition');
    EasyLoading.dismiss();
    if (mounted && result.isSuccess == true) {
      if (result.data != null) {
        this.handlerConfigData(result.data);
      }
    }
  }

  void handlerConfigData(SelectObjectFilertRootModel rootModel) {
    /// 选择范围
    SelectObjectFilterConfigModel groupModel = SelectObjectFilterConfigModel(
      itemKey: ObjectFilterKey.groupKeyName,
      itemTitle: '选择范围',
    );
    this.dataSource.add(groupModel);

    /// 选择区域
    SelectObjectFilterConfigModel areaModel = SelectObjectFilterConfigModel(
      itemKey: ObjectFilterKey.areaKeyName,
      itemTitle: '选择区域',
    );
    this.dataSource.add(areaModel);

    /// 客户等级 (地图的筛选展示)
    if (rootModel.levelType != null && widget.isMapFilter) {
      String? defaultId = rootModel.levelType
          ?.firstWhere((element) => "${element.selected}" == "1",
              orElse: () => SelectObjectFilterData())
          .code
          .toString();
      SelectObjectFilterConfigModel configModel = SelectObjectFilterConfigModel(
        itemKey: ObjectFilterKey.levelKeyName,
        itemTitle: '客户等级',
        contents: (rootModel.levelType ?? [])
            .map((e) =>
                CommonFilterItemEntry(text: "${e.text}", code: "${e.code}"))
            .toList(),
        defaultId: defaultId,
        isAllowMultipleSelection: true,
        isMutexByDefault: true,
        isAllowClean: false,
      );
      this.dataSource.add(configModel);
    }

    /// 下单情况
    if (rootModel.orderCondition != null) {
      String? defaultId = rootModel.orderCondition
          ?.firstWhere((element) => "${element.selected}" == "1",
              orElse: () => SelectObjectFilterData())
          .code
          .toString();
      SelectObjectFilterConfigModel configModel = SelectObjectFilterConfigModel(
        itemKey: ObjectFilterKey.orderKeyName,
        itemTitle: '下单情况',
        contents: (rootModel.orderCondition ?? [])
            .map((e) =>
                CommonFilterItemEntry(text: "${e.text}", code: "${e.code}"))
            .toList(),
        defaultId: defaultId,
        isAllowMultipleSelection: false,
        isMutexByDefault: true,
        isAllowClean: false,
      );
      this.dataSource.add(configModel);
    }

    /// 拜访情况
    if (rootModel.visitCondition != null) {
      String? defaultId = rootModel.visitCondition
          ?.firstWhere((element) => "${element.selected}" == "1",
              orElse: () => SelectObjectFilterData())
          .code
          .toString();
      SelectObjectFilterConfigModel configModel = SelectObjectFilterConfigModel(
        itemKey: ObjectFilterKey.visitKeyName,
        itemTitle: '拜访情况',
        contents: (rootModel.visitCondition ?? [])
            .map((e) =>
                CommonFilterItemEntry(text: "${e.text}", code: "${e.code}"))
            .toList(),
        defaultId: defaultId,
        isAllowMultipleSelection: false,
        isMutexByDefault: true,
        isAllowClean: false,
      );
      this.dataSource.add(configModel);
    }

    /// 客户类型
    if (rootModel.customerType != null) {
      String? defaultId = rootModel.customerType
          ?.firstWhere((element) => "${element.selected}" == "1",
              orElse: () => SelectObjectFilterData())
          .code
          .toString();
      SelectObjectFilterConfigModel configModel = SelectObjectFilterConfigModel(
        itemKey: ObjectFilterKey.typeKeyName,
        itemTitle: '客户类型',
        contents: (rootModel.customerType ?? [])
            .map((e) =>
                CommonFilterItemEntry(text: "${e.text}", code: "${e.code}"))
            .toList(),
        defaultId: defaultId,
        isAllowMultipleSelection: true,
        isMutexByDefault: true,
        isAllowClean: false,
      );
      this.dataSource.add(configModel);
    }

    /// 智慧脸客户
    if (rootModel.saasUserFlag != null) {
      String? defaultId = rootModel.saasUserFlag
          ?.firstWhere((element) => "${element.selected}" == "1",
          orElse: () => SelectObjectFilterData())
          .code
          .toString();
      SelectObjectFilterConfigModel saasConfigModel = SelectObjectFilterConfigModel(
        itemKey: ObjectFilterKey.saasUserFlag,
        itemTitle: '智慧脸客户',
        contents: (rootModel.saasUserFlag ?? [])
            .map((e) =>
            CommonFilterItemEntry(text: "${e.text}", code: "${e.code}"))
            .toList(),
        defaultId: defaultId,
        isAllowMultipleSelection: false,
        isMutexByDefault: true,
        isAllowClean: false,
      );
      this.dataSource.add(saasConfigModel);
    }
    setState(() {});
  }

  /// 特殊处理客户状态的选中参数
  List<String> getCustomerStatusCode() {
    if (this.filterMap.keys.contains(ObjectFilterKey.statusKeyName)) {
      String selectValue = this.filterMap[ObjectFilterKey.statusKeyName] ?? "";
      selectValue = selectValue.replaceAll("1,2", "1000");
      List<String> ids = selectValue.split(",");
      int index = ids.indexOf("1000");
      if (index != -1) {
        ids[index] = "1,2";
      }
      return ids;
    }
    return [];
  }

  /// 获取当前选中的id
  List<String> getSelectIdsForKey(String itemKey) {
    if (itemKey == ObjectFilterKey.statusKeyName) {
      return this.getCustomerStatusCode();
    }
    return this.filterMap[itemKey]?.split(",") ?? [];
  }

  /// 判断是否是选择类型
  bool isSelectItem(SelectObjectFilterConfigModel configModel) {
    return configModel.itemKey == ObjectFilterKey.groupKeyName ||
        configModel.itemKey == ObjectFilterKey.areaKeyName;
  }
}
