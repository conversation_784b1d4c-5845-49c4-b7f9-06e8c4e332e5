import 'random_string_generator.dart';

/// 全局随机字符串工具方法
/// 提供便捷的全局访问方式

/// 生成6位随机字符串（0-9, a-z, A-Z）
/// 
/// 示例：randomString6() => "a1B2c3"
String randomString6() {
  return RandomStringGenerator.generate6();
}

/// 生成8位随机字符串（0-9, a-z, A-Z）
/// 
/// 示例：randomString8() => "a1B2c3D4"
String randomString8() {
  return RandomStringGenerator.generate8();
}

/// 生成指定长度的随机字符串（0-9, a-z, A-Z）
/// 
/// [length] 字符串长度
/// 示例：randomString(10) => "a1B2c3D4e5"
String randomString(int length) {
  return RandomStringGenerator.generate(length);
}

/// 生成带前缀的随机字符串
/// 
/// [prefix] 前缀
/// [length] 随机部分长度
/// 示例：randomStringWithPrefix("USER_", 6) => "USER_a1B2c3"
String randomStringWithPrefix(String prefix, int length) {
  return RandomStringGenerator.generateWithPrefix(prefix, length);
}

/// 生成带时间戳的随机字符串
/// 
/// [length] 随机部分长度
/// 示例：randomStringWithTimestamp(4) => "1703123456789a1B2"
String randomStringWithTimestamp(int length) {
  return RandomStringGenerator.generateWithTimestamp(length);
}

/// 生成UUID风格的随机字符串
/// 
/// 示例：randomUUID() => "a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6"
String randomUUID() {
  return RandomStringGenerator.generateUUIDStyle();
}

/// 批量生成随机字符串
/// 
/// [count] 数量
/// [length] 长度
/// 示例：randomStringBatch(3, 4) => ["a1B2", "c3D4", "e5F6"]
List<String> randomStringBatch(int count, int length) {
  return RandomStringGenerator.generateBatch(count, length);
}

/// 生成不重复的随机字符串集合
/// 
/// [count] 数量
/// [length] 长度
/// 示例：randomStringUniqueSet(3, 4) => {"a1B2", "c3D4", "e5F6"}
Set<String> randomStringUniqueSet(int count, int length) {
  return RandomStringGenerator.generateUniqueSet(count, length);
}

/// 验证字符串是否为有效的随机字符串格式
/// 
/// [str] 要验证的字符串
/// 示例：isValidRandomString("a1B2") => true, isValidRandomString("a1B2@") => false
bool isValidRandomString(String str) {
  return RandomStringGenerator.isValidRandomString(str);
}

/// 常用的随机字符串生成器类
/// 
/// 使用示例：
/// ```dart
/// // 基础用法
/// String id6 = randomString6();        // 生成6位
/// String id8 = randomString8();        // 生成8位
/// String custom = randomString(12);    // 生成12位
/// 
/// // 高级用法
/// String userID = randomStringWithPrefix("USER_", 6);     // USER_a1B2c3
/// String sessionID = randomStringWithTimestamp(4);        // 1703123456789a1B2
/// String uuid = randomUUID();                             // a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6
/// 
/// // 批量生成
/// List<String> ids = randomStringBatch(5, 6);             // 生成5个6位字符串
/// Set<String> uniqueIds = randomStringUniqueSet(10, 8);   // 生成10个不重复的8位字符串
/// 
/// // 验证
/// bool valid = isValidRandomString("a1B2c3");             // true
/// ```
class GlobalRandomUtils {
  /// 私有构造函数，防止实例化
  GlobalRandomUtils._();
  
  /// 获取字符集信息
  static String get characterSet => RandomStringGenerator.getCharacterSet();
  
  /// 获取字符集长度
  static int get characterSetLength => RandomStringGenerator.getCharacterSetLength();
  
  /// 生成6位随机字符串的静态方法
  static String string6() => randomString6();
  
  /// 生成8位随机字符串的静态方法
  static String string8() => randomString8();
  
  /// 生成指定长度随机字符串的静态方法
  static String string(int length) => randomString(length);
}
