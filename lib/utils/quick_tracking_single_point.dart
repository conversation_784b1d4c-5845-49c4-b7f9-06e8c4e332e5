﻿import 'package:qt_common_sdk/qt_common_sdk.dart';

/// Quick Tracking SDK 单点上报工具类
class QuickTrackingSinglePoint {
  
  /// 初始化SDK（单点上报模式）
  static Future<void> initSDK({
    required String androidAppKey,
    required String iosAppKey,
    required String channel,
    bool enableLog = false,
  }) async {
    try {
      await QTCommonSdk.initCommon(androidAppKey, iosAppKey, channel);
      QTCommonSdk.setLogEnabled(enableLog);
      print('Quick Tracking SDK 单点上报模式初始化成功');
    } catch (e) {
      print('Quick Tracking SDK 初始化失败: $e');
    }
  }

  /// 用户登录事件上报
  static void reportUserLogin({
    required String userId,
    String? userType,
    String? loginMethod,
    Map<String, dynamic>? extraData,
  }) {
    try {
      QTCommonSdk.onProfileSignIn(userId);
      
      Map<String, dynamic> properties = {
        'user_id': userId,
        'login_time': DateTime.now().millisecondsSinceEpoch,
      };
      
      if (userType != null) properties['user_type'] = userType;
      if (loginMethod != null) properties['login_method'] = loginMethod;
      if (extraData != null) properties.addAll(extraData);
      
      QTCommonSdk.onEvent('user_login', properties);
      print('Quick Tracking: 用户登录事件上报成功 - $userId');
    } catch (e) {
      print('Quick Tracking: 用户登录事件上报失败 - $e');
    }
  }

  /// 按钮点击事件上报
  static void reportButtonClick({
    required String buttonName,
    String? pageName,
    String? buttonType,
    Map<String, dynamic>? extraData,
  }) {
    try {
      Map<String, dynamic> properties = {
        'button_name': buttonName,
        'click_time': DateTime.now().millisecondsSinceEpoch,
      };
      
      if (pageName != null) properties['page_name'] = pageName;
      if (buttonType != null) properties['button_type'] = buttonType;
      if (extraData != null) properties.addAll(extraData);
      
      QTCommonSdk.onEvent('button_click', properties);
      print('Quick Tracking: 按钮点击事件上报成功 - $buttonName');
    } catch (e) {
      print('Quick Tracking: 按钮点击事件上报失败 - $e');
    }
  }

  /// 页面访问事件上报
  static void reportPageView({
    required String pageName,
    String? pageType,
    String? sourcePage,
    Map<String, dynamic>? extraData,
  }) {
    try {
      Map<String, dynamic> properties = {
        'page_name': pageName,
        'view_time': DateTime.now().millisecondsSinceEpoch,
      };
      
      if (pageType != null) properties['page_type'] = pageType;
      if (sourcePage != null) properties['source_page'] = sourcePage;
      if (extraData != null) properties.addAll(extraData);
      
      QTCommonSdk.onEvent('page_view', properties);
      print('Quick Tracking: 页面访问事件上报成功 - $pageName');
    } catch (e) {
      print('Quick Tracking: 页面访问事件上报失败 - $e');
    }
  }

  /// 商品浏览事件上报
  static void reportProductView({
    required String productId,
    required String productName,
    String? category,
    String? brand,
    double? price,
    Map<String, dynamic>? extraData,
  }) {
    try {
      Map<String, dynamic> properties = {
        'product_id': productId,
        'product_name': productName,
        'view_time': DateTime.now().millisecondsSinceEpoch,
      };
      
      if (category != null) properties['category'] = category;
      if (brand != null) properties['brand'] = brand;
      if (price != null) properties['price'] = price;
      if (extraData != null) properties.addAll(extraData);
      
      QTCommonSdk.onEvent('product_view', properties);
      print('Quick Tracking: 商品浏览事件上报成功 - $productName');
    } catch (e) {
      print('Quick Tracking: 商品浏览事件上报失败 - $e');
    }
  }

  /// 搜索事件上报
  static void reportSearch({
    required String keyword,
    String? searchType,
    int? resultCount,
    String? searchCategory,
    Map<String, dynamic>? extraData,
  }) {
    try {
      Map<String, dynamic> properties = {
        'keyword': keyword,
        'search_time': DateTime.now().millisecondsSinceEpoch,
      };
      
      if (searchType != null) properties['search_type'] = searchType;
      if (resultCount != null) properties['result_count'] = resultCount;
      if (searchCategory != null) properties['search_category'] = searchCategory;
      if (extraData != null) properties.addAll(extraData);
      
      QTCommonSdk.onEvent('search_action', properties);
      print('Quick Tracking: 搜索事件上报成功 - $keyword');
    } catch (e) {
      print('Quick Tracking: 搜索事件上报失败 - $e');
    }
  }

  /// 自定义事件上报
  static void reportCustomEvent({
    required String eventName,
    Map<String, dynamic>? properties,
  }) {
    try {
      Map<String, dynamic> eventProperties = {
        'event_time': DateTime.now().millisecondsSinceEpoch,
      };
      
      if (properties != null) {
        eventProperties.addAll(properties);
      }
      
      QTCommonSdk.onEvent(eventName, eventProperties);
      print('Quick Tracking: 自定义事件上报成功 - $eventName');
    } catch (e) {
      print('Quick Tracking: 自定义事件上报失败 - $eventName, 错误: $e');
    }
  }

  /// 设置用户属性
  static void setUserProperties(Map<String, dynamic> properties) {
    try {
      QTCommonSdk.registerGlobalProperties(properties);
      print('Quick Tracking: 用户属性设置成功 - $properties');
    } catch (e) {
      print('Quick Tracking: 用户属性设置失败 - $e');
    }
  }

  /// 清除用户属性
  static void clearUserProperties() {
    try {
      QTCommonSdk.clearGlobalProperties();
      print('Quick Tracking: 用户属性清除成功');
    } catch (e) {
      print('Quick Tracking: 用户属性清除失败 - $e');
    }
  }
}
