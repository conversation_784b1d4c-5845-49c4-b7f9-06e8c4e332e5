import 'package:json_annotation/json_annotation.dart';

part 'user_info_data.g.dart';

@JsonSerializable()
class UserInfoData {
  String? department;
  String? email;
  String? name;
  String? phone;
  String? realName;
  int? roleType;
  String? sysUserId;
  String? jobNumber;
  String? token;
  String? sessionId;

  UserInfoData();

  factory UserInfoData.fromJson(Map<String, dynamic> json) {
    print('qqqqq');
    try {
      return _$UserInfoDataFromJson(json);
    } catch (e) {
      print('JSON解析错误: $e');
      print('JSON数据: $json');
      // 创建一个空的UserInfoData对象，避免崩溃
      return UserInfoData();
    }
  }

  UserInfoData fromJsonMap(Map<String, dynamic> json) {
    return _$UserInfoDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$UserInfoDataToJson(this);
  }
}
