import 'dart:convert';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_data.dart';

class UserInfoUtil {
  /// 角色类型 0 :BD 1:BDM 2:跟进人 3:跟进人BDM
  static final int TYPE_BD = 0;
  static final int TYPE_BDM = 1;
  static final int TYPE_GJR = 2;
  static final int TYPE_GJR_BDM = 3;

  static Future<bool> isBDMOrGJRBDM() async {
    var userInfoData = await getUserInfo();
    if (userInfoData != null &&
        (userInfoData.roleType == TYPE_BDM ||
            userInfoData.roleType == TYPE_GJR_BDM)) {
      return true;
    } else {
      return false;
    }
  }

  /// 是否是跟进人权限
  static Future<bool> isFollowPeople() async {
    var userInfoData = await getUserInfo();
    if (userInfoData != null &&
        (userInfoData.roleType == TYPE_GJR ||
            userInfoData.roleType == TYPE_GJR_BDM)) {
      return true;
    } else {
      return false;
    }
  }

  static Future<String> sysUserId() async {
    var userInfoData = await getUserInfo();
    if (userInfoData != null && userInfoData.sysUserId != null) {
      return userInfoData.sysUserId!;
    }
    return "";
  }

  static Future<String> roleType() async {
    var userInfoData = await getUserInfo();
    if (userInfoData != null && userInfoData.roleType != null) {
      return "${userInfoData.roleType}";
    }
    return "";
  }

  static Future<UserInfoData?> getUserInfo() async {
    var userInfoJson = await XYYContainer.bridgeCall("user_info");
    try {
      var decodeMap = json.decode(userInfoJson);
      print('startqiyu:${decodeMap}');
      var userInfoData = UserInfoData.fromJson(decodeMap);
      print('startqiyu1:${decodeMap}');
      return userInfoData;
    } catch (e) {
      return null;
    }
  }
}
