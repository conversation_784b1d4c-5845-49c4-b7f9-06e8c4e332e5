import 'dart:math';

/// 全局随机字符串生成器
/// 支持生成包含 0-9, a-z, A-Z 的随机字符串
class RandomStringGenerator {
  /// 字符集：数字 + 小写字母 + 大写字母
  static const String _chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
  
  /// 随机数生成器
  static final Random _random = Random();

  /// 生成指定长度的随机字符串
  /// 
  /// [length] 字符串长度
  /// 返回包含 0-9, a-z, A-Z 的随机字符串
  static String generate(int length) {
    if (length <= 0) {
      throw ArgumentError('长度必须大于0');
    }
    
    return String.fromCharCodes(
      Iterable.generate(
        length,
        (_) => _chars.codeUnitAt(_random.nextInt(_chars.length)),
      ),
    );
  }

  /// 生成6位随机字符串
  /// 
  /// 返回6位包含 0-9, a-z, A-Z 的随机字符串
  static String generate6() {
    return generate(6);
  }

  /// 生成8位随机字符串
  /// 
  /// 返回8位包含 0-9, a-z, A-Z 的随机字符串
  static String generate8() {
    return generate(8);
  }

  /// 生成自定义长度的随机字符串（带前缀）
  /// 
  /// [prefix] 前缀字符串
  /// [length] 随机部分的长度
  /// 返回 前缀 + 随机字符串
  static String generateWithPrefix(String prefix, int length) {
    return prefix + generate(length);
  }

  /// 生成带时间戳的随机字符串
  /// 
  /// [length] 随机部分的长度
  /// 返回 时间戳 + 随机字符串
  static String generateWithTimestamp(int length) {
    String timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    return timestamp + generate(length);
  }

  /// 生成UUID风格的随机字符串（8-4-4-4-12格式）
  /// 
  /// 返回类似 UUID 格式的随机字符串，如：a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6
  static String generateUUIDStyle() {
    return '${generate(8)}-${generate(4)}-${generate(4)}-${generate(4)}-${generate(12)}';
  }

  /// 批量生成随机字符串
  /// 
  /// [count] 生成数量
  /// [length] 每个字符串的长度
  /// 返回随机字符串列表
  static List<String> generateBatch(int count, int length) {
    if (count <= 0) {
      throw ArgumentError('数量必须大于0');
    }
    
    return List.generate(count, (_) => generate(length));
  }

  /// 生成不重复的随机字符串集合
  /// 
  /// [count] 生成数量
  /// [length] 每个字符串的长度
  /// [maxAttempts] 最大尝试次数（防止无限循环）
  /// 返回不重复的随机字符串集合
  static Set<String> generateUniqueSet(int count, int length, {int maxAttempts = 10000}) {
    if (count <= 0) {
      throw ArgumentError('数量必须大于0');
    }
    
    Set<String> uniqueStrings = <String>{};
    int attempts = 0;
    
    while (uniqueStrings.length < count && attempts < maxAttempts) {
      uniqueStrings.add(generate(length));
      attempts++;
    }
    
    if (uniqueStrings.length < count) {
      throw StateError('无法在 $maxAttempts 次尝试内生成 $count 个不重复的字符串');
    }
    
    return uniqueStrings;
  }

  /// 验证字符串是否只包含有效字符（0-9, a-z, A-Z）
  /// 
  /// [str] 要验证的字符串
  /// 返回是否只包含有效字符
  static bool isValidRandomString(String str) {
    if (str.isEmpty) return false;
    
    for (int i = 0; i < str.length; i++) {
      if (!_chars.contains(str[i])) {
        return false;
      }
    }
    return true;
  }

  /// 获取字符集信息
  /// 
  /// 返回当前使用的字符集
  static String getCharacterSet() {
    return _chars;
  }

  /// 获取字符集长度
  /// 
  /// 返回字符集的总长度
  static int getCharacterSetLength() {
    return _chars.length;
  }
}
