import 'dart:convert';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/performance/data/team_performance_list_model.dart';
import 'package:XyyBeanSproutsFlutter/performance/data/team_performance_list_params.dart';
import 'package:XyyBeanSproutsFlutter/performance/page/base/team_performance_list_base_page.dart';
import 'package:XyyBeanSproutsFlutter/performance/widget/team_performance_list_footer.dart';
import 'package:XyyBeanSproutsFlutter/performance/widget/team_performance_list_footer_to_rank.dart';
import 'package:XyyBeanSproutsFlutter/performance/widget/team_performance_list_item.dart';
import 'package:flutter/cupertino.dart';

/// 团队业绩列表
class TeamPerformanceListPage extends TeamPerformanceListBasePage {
  String? displayName;

  TeamPerformanceListPage({this.displayName, String? paramsModelJson}) {
    if (paramsModelJson != null) {
      // 将 (lwqlwq) 替换回 :
      this.paramsModelJson = paramsModelJson.replaceAll('(lwqlwq)', ':');
    } else {
      this.paramsModelJson = paramsModelJson;
    }
  }

  @override
  TeamPerformanceListBasePageState<TeamPerformanceListBasePage> initState() {
    return TeamPerformanceListPageState();
  }
}

class TeamPerformanceListPageState extends TeamPerformanceListBasePageState<TeamPerformanceListPage> {
  /// 是否显示底部widget (查看下属所有KA，查看下属所有BD)
  var isShowBottom = false;
  ///是否展示跳转到排行榜的footer
  var isShowFooterToRank = false;

  @override
  void onCreate() {
    super.onCreate();
    track("mc-mine-teamper");
  }

  @override
  String getTitleName() => this.widget.displayName ?? '团队业绩';


  @override
  Widget onListFooter() {
    if (isShowFooterToRank) {
      return TeamPerformanceListFooterToRankWidget(onFooterClickRankCallback: () {
        XYYContainer.open('/team_performance_rank_list_page?userLevel=$USER_TYPE_M&rankUserType=$USER_TYPE_BD');
      });
    }
    if (!isShowBottom) {
      return Container();
    }
    var paramsModelMap = paramsModel.toJson();
    paramsModelMap['groupId'] = null;
    return TeamPerformanceListFooterWidget(onFooterClickBDCallback: () {
      track("mc-mine-teamBD");
      String paramsJson = json.encode(paramsModelMap).replaceAll(":", "(lwqlwq)");
      String routerPath = "/team_performance_ka_or_bd_list_page?listType=$USER_TYPE_BD&paramsModelJson=$paramsJson";
      routerPath = Uri.encodeFull(routerPath);
      XYYContainer.open(routerPath);
    }, onFooterClickKACallback: () {
      track("mc-mine-teamKA");
      String paramsJson = json.encode(paramsModelMap).replaceAll(":", "(lwqlwq)");
      String routerPath = "/team_performance_ka_or_bd_list_page?listType=$USER_TYPE_KA&paramsModelJson=$paramsJson";
      routerPath = Uri.encodeFull(routerPath);
      XYYContainer.open(routerPath);
    });
  }

  @override
  Map<String, dynamic> getRequestParams() => paramsModel.toJson();

  @override
  String getRequestUrl() => 'group/gmv/list';

  @override
  void onRequestListData(TeamPerformanceListDataModel? model) {
    isShowFooterToRank = model?.lastPageFlag?? false;
    isShowBottom = (model?.mangerFlag ?? false) && paramsModel.timeType == TIME_TYPE_THIS_MOUTH;
  }

  @override
  bool get isNoMore => true;

  @override
  Widget onItemClick(BuildContext context, int index) {
    return GestureDetector(
      onTap: () {
        var listItemModel = dataSource[index] as TeamPerformanceListItemModel;
        if (listItemModel.haveChild) {
          Map paramsModelMap = paramsModel.toJson();
          paramsModelMap.remove('gmvFilter');//gmvFilter顶部筛选参数只对当面页面生效，不往下传递
          paramsModelMap['groupId'] = listItemModel.groupId;
          String paramsJson = json.encode(paramsModelMap).replaceAll(":", "(lwqlwq)");
          String routerPath = "/team_performance_list_page?paramsModelJson=$paramsJson&displayName=${listItemModel.displayName}";
          routerPath = Uri.encodeFull(routerPath);
          XYYContainer.open(routerPath);
        }
      },
      behavior: HitTestBehavior.opaque,
      child: TeamPerformanceListItem(
        index,
        dataSource[index],
        isShowContent: isShowContent(index),
      ),
    );
  }

  /// groupId非以字符'P'开头或者以'P'且没有子节点的显示GMV内容
  bool isShowContent(int index) {
    var listItemModel = dataSource[index] as TeamPerformanceListItemModel;
    if (listItemModel.groupId == null) return true;
    var groupStartWithP = (listItemModel.groupId as String).startsWith('P');
    return !groupStartWithP || (groupStartWithP && !listItemModel.haveChild);
  }

  @override
  get isShowDateFilter => true;

  @override
  void filterClick() {
    track("mc-mine-teamorg");
    XYYContainer.open(
        'xyy://crm-app.ybm100.com/executor?needSetResult=true&canChooseDepartment=true&canPickPerson=false',
        callback: (params) {
          if (params == null || params['id'] == null) return;
          var paramsModelMap = paramsModel.toJson();
          paramsModelMap['groupId'] = params['id'];
          String paramsJson = json.encode(paramsModelMap).replaceAll(":", "(lwqlwq)");
          String routerPath = "/team_performance_list_page?paramsModelJson=$paramsJson&displayName=${params['name']}";
          routerPath = Uri.encodeFull(routerPath);
          XYYContainer.open(routerPath);
        });
  }
}
