import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/performance/data/team_performance_list_model.dart';
import 'package:XyyBeanSproutsFlutter/performance/data/team_performance_list_params.dart';
import 'package:XyyBeanSproutsFlutter/performance/page/base/team_performance_list_base_page.dart';
import 'package:XyyBeanSproutsFlutter/performance/widget/team_performance_list_footer_to_rank.dart';
import 'package:XyyBeanSproutsFlutter/performance/widget/team_performance_list_item.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/src/widgets/framework.dart';

/// 团队业绩列表
class TeamPerformanceKAOrBDListPage extends TeamPerformanceListBasePage {

  String listType;

  TeamPerformanceKAOrBDListPage({required this.listType, required String paramsModelJson}) {
    if (paramsModelJson != null) {
      // 将 (lwqlwq) 替换回 :
      this.paramsModelJson = paramsModelJson.replaceAll('(lwqlwq)', ':');
    } else {
      this.paramsModelJson = paramsModelJson;
    }
  }

  @override
  TeamPerformanceKAOrBDListPageState initState() {
    return TeamPerformanceKAOrBDListPageState();
  }
}

class TeamPerformanceKAOrBDListPageState extends TeamPerformanceListBasePageState<TeamPerformanceKAOrBDListPage> {

  var isShowBottom = false;

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(
      getTitleName(),
      leftType: LeftButtonType.back,
    );
  }

  @override
  Widget onListFooter() {
    return TeamPerformanceListFooterToRankWidget(onFooterClickRankCallback: () {
      XYYContainer.open('/team_performance_rank_list_page?userLevel=$USER_TYPE_M&rankUserType=$USER_TYPE_BD');
    });
  }

  @override
  Future<void> refreshList() {
    offset = 1;
    return super.refreshList();
  }

  @override
  Map<String, dynamic> getRequestParams() {
    var paramsMap = paramsModel.toJson();
    paramsMap['userType'] = this.widget.listType;
    paramsMap['offset'] = '$offset';
    return paramsMap;
  }

  @override
  String getRequestUrl() => 'group/gmv/allUser';

  @override
  void onRequestListData(TeamPerformanceListDataModel? model) {
    var hasNextPage = model?.hasNextPage;
    isLastPage = !hasNextPage;
    if (!isLastPage) {
      offset++;
    }
  }

  @override
  bool get isNoMore => isLastPage;

  @override
  Widget onItemClick(BuildContext context, int index) {
    return TeamPerformanceListItem(index, dataSource[index]);
  }

  @override
  String getTitleName() {
    return this.widget.listType == USER_TYPE_KA? 'KA': 'BD';
  }

  @override
  get isShowDateFilter => false;
}