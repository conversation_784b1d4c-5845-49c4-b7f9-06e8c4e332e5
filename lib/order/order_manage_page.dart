import 'dart:convert';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/order/bean/order_list_item_data.dart';
import 'package:XyyBeanSproutsFlutter/order/bean/order_manage_data.dart';
import 'package:XyyBeanSproutsFlutter/order/bean/order_tag_data.dart';
import 'package:XyyBeanSproutsFlutter/order/widgets/order_list_item_widget.dart';
import 'package:XyyBeanSproutsFlutter/order/widgets/order_list_marquee.dart';
import 'package:XyyBeanSproutsFlutter/order/widgets/order_refund_list_item_widget.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';

import 'bean/order_status_tag_data.dart';

const ORDER_PAGE_TYPE_HOME = "0";
const ORDER_PAGE_TYPE_MINE = "1";
const ORDER_PAGE_TYPE_REFUND = "3";

class OrderManagePage extends BasePage {
  final String? pageType; // 订单状态
  final String? dateType; // 时间类型
  final String? id; // 执行人id
  final String? isGroup; // 是否为组织
  final String? areaName;
  final String? statusList; // 订单状态 字符串， 兼容首页跳转使用
  final String? exceptionTypeList; // 卡单状态字符串，兼容首页跳转使用

  /// 首页使用传过来的参数
  final String? startCreateTime;
  final String? endCreateTime;
  final String? sceneType;

  OrderManagePage(
    this.pageType, {
    this.dateType,
    this.id,
    this.isGroup,
    this.areaName,
    this.statusList,
    this.exceptionTypeList,
    this.startCreateTime,
    this.endCreateTime,
    this.sceneType,
  });

  @override
  BaseState<StatefulWidget> initState() {
    return OrderManagePageState();
  }
}

class OrderManagePageState extends BaseState<OrderManagePage> {
  var _statusModel = OrderStatusModel();
  late OrderListModel _listModel;
  var _refreshController = EasyRefreshController();
  var _scrollController = ScrollController();

  PageStateWidget? pageStateWidget;


  @override
  void onDestroy() {
    super.onDestroy();
    _statusModel.dispose();
    _listModel.dispose();
    _refreshController.dispose();
    _scrollController.dispose();
  }

  @override
  void onCreate() {
    _statusModel.widegtStatus = widget.statusList;

    pageStateWidget = new PageStateWidget();
    _listModel = OrderListModel(_scrollController, _refreshController,
        widget.pageType, widget.dateType, widget.id, widget.isGroup);
    if (widget.areaName != null) {
      _listModel.filterParams['name'] = widget.areaName;
    }
    if (widget.exceptionTypeList != null) {
      _listModel.filterParams['exceptionTypes'] = widget.exceptionTypeList;
    }

    // if (widget.sceneType != null && widget.startCreateTime != null && widget.endCreateTime != null) {
    //   _listModel.filterParams['sceneType'] = widget.sceneType;
    //
    //   _listModel.filterParams['startCreateTime'] = widget.startCreateTime;
    //   _listModel.filterParams['endCreateTime'] = widget.endCreateTime;
    // }
    if (widget.sceneType != null && widget.startCreateTime != null && widget.endCreateTime != null) {
      _listModel.filterParams['sceneType'] = widget.sceneType;
      // 判空并将时间戳转为格式化时间字符串再赋值
      if (widget.startCreateTime != null && widget.startCreateTime!.isNotEmpty) {
        try {
          var sTime = int.tryParse(widget.startCreateTime!);
          if (sTime != null) {
            var strTime = DateTime.fromMillisecondsSinceEpoch(sTime);
            var startValueTime = "${strTime.year.toString().padLeft(4, '0')}-${strTime.month.toString().padLeft(2, '0')}-${strTime.day.toString().padLeft(2, '0')} ${strTime.hour.toString().padLeft(2, '0')}:${strTime.minute.toString().padLeft(2, '0')}:00";
            _listModel.filterParams['startCreateTime'] = startValueTime;
          } else {
            _listModel.filterParams['startCreateTime'] = widget.startCreateTime;
          }
        } catch (e) {
          _listModel.filterParams['startCreateTime'] = widget.startCreateTime;
        }
      }
      if (widget.endCreateTime != null && widget.endCreateTime!.isNotEmpty) {
        try {
          var eTime = int.tryParse(widget.endCreateTime!);
          if (eTime != null) {
            var endTime = DateTime.fromMillisecondsSinceEpoch(eTime);
            var endValueTime = "${endTime.year.toString().padLeft(4, '0')}-${endTime.month.toString().padLeft(2, '0')}-${endTime.day.toString().padLeft(2, '0')} ${endTime.hour.toString().padLeft(2, '0')}:${endTime.minute.toString().padLeft(2, '0')}:00";
            _listModel.filterParams['endCreateTime'] = endValueTime;
          } else {
            _listModel.filterParams['endCreateTime'] = widget.endCreateTime;
          }
        } catch (e) {
          _listModel.filterParams['endCreateTime'] = widget.endCreateTime;
        }
      }
    }
    _listModel.requestSmallImageHost();
    super.onCreate();
  }

  @override
  List<SingleChildWidget> getProvider() {
    return [
      ChangeNotifierProvider<OrderStatusModel>(
          create: (context) => _statusModel),
      ChangeNotifierProvider<OrderListModel>(create: (context) => _listModel)
    ];
  }

  @override
  Widget buildWidget(BuildContext context) {
    requestStatusList(_listModel);
    return Container(
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          buildHeaderView(),
          Expanded(
            child: Container(
              color: Color(0xffefeff4),
              child: buildListView(),
            ),
          ),
          Visibility(
            visible: widget.pageType != ORDER_PAGE_TYPE_REFUND,
            child: Container(
              height: 30,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: Color(0xfffff7ef),
              ),
              child: Consumer<OrderListModel>(builder: (context, model, child) {
                return OrderListMarquee(
                  tips: model.tips,
                );
              }),
            ),
          ),
          Container(
            color: Colors.white,
            height: 44,
            padding: EdgeInsets.only(right: 15),
            alignment: Alignment.centerRight,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Consumer<OrderListModel>(
                  builder: (context, model, child) {
                    return Text.rich(
                      TextSpan(
                        text: widget.pageType == ORDER_PAGE_TYPE_REFUND
                            ? "退款金额 ¥"
                            : "订单金额 ¥",
                        style: TextStyle(
                          fontSize: 13,
                          color: Color(0xff292933),
                          fontWeight: FontWeight.w500,
                        ),
                        children: getFormatMoneyTextSpan(
                            _listModel.totalMoneyText ?? "-"),
                      ),
                    );
                  },
                )
              ],
            ),
          )
        ],
      ),
    );
  }

  List<TextSpan> getFormatMoneyTextSpan(String? moneyText) {
    String integerPlace = "";
    String decimalPlace = "";
    if (moneyText != null) {
      var indexOf = moneyText.indexOf(".");
      if (indexOf != -1) {
        integerPlace = moneyText.substring(0, indexOf);
        decimalPlace = moneyText.substring(indexOf);
      } else {
        integerPlace = moneyText;
        decimalPlace = "";
      }
    }
    return [
      TextSpan(
        text: integerPlace,
        style: TextStyle(
          fontSize: 16,
          color: Color(0xff292933),
          fontWeight: FontWeight.w500,
        ),
      ),
      TextSpan(
        text: decimalPlace,
        style: TextStyle(
          fontSize: 13,
          color: Color(0xff292933),
          fontWeight: FontWeight.w500,
        ),
      )
    ];
  }

  buildHeaderView() {
    return Consumer<OrderStatusModel>(builder: (context, model, child) {
      return model.statusList?.isNotEmpty != true
          ? Container()
          : Container(
              padding: EdgeInsets.fromLTRB(15, 15, 0, 10),
              color: Colors.white,
              width: double.infinity,
              height: (model.isExpand ? null : 52),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Expanded(
                      child: Wrap(
                    spacing: 10,
                    runSpacing: 10,
                    children: List.generate(
                      model.statusList?.length ?? 0,
                      (index) {
                        return _buildItemTag(index);
                      },
                    ),
                  )),
                  GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () {
                      model.toggleExpandStatus();
                    },
                    child: Container(
                      height: 30,
                      child: Container(
                          padding: EdgeInsets.only(right: 25, left: 10),
                          child: Image.asset(
                            model.isExpand
                                ? 'assets/images/order/order_up_gray.png'
                                : 'assets/images/order/order_down_gray.png',
                            width: 14,
                            height: 30,
                          )),
                    ),
                  )
                ],
              ));
    });
  }

  buildListView() {
    return Consumer<OrderListModel>(builder: (context, model, child) {
      var emptyWidget = getEmptyWidget();
      return Container(
        color: emptyWidget == null ? Colors.transparent : Colors.white,
        child: EasyRefresh.custom(
            controller: _refreshController,
            scrollController: _scrollController,
            enableControlFinishRefresh: true,
            enableControlFinishLoad: true,
            onRefresh: () async {
              _listModel.requestListModel(
                  true, _statusModel.getOrderStatusStr());
            },
            onLoad: !(_listModel.isLastPage ?? false) &&
                    _listModel.list?.isNotEmpty == true
                ? () async {
                    _listModel.requestListModel(
                        false, _statusModel.getOrderStatusStr());
                  }
                : null,
            slivers: [
              SliverPadding(padding: EdgeInsets.only(top: 5)),
              SliverList(
                delegate: SliverChildBuilderDelegate(
                  (BuildContext context, int index) {
                    //创建列表项
                    return buildOrderItem(index);
                  },
                  childCount: _listModel.list?.length ?? 0,
                ),
              ),
              SliverPadding(
                padding: EdgeInsets.only(top: 5),
              ),
            ],
            emptyWidget: emptyWidget),
      );
    });
  }

  @override
  PreferredSizeWidget getTitleBar(BuildContext context) {
    return CommonTitleBar(
      getTitleName(),
      rightButtons: [buildFilterButton(context), buildSearchButton()],
    );
  }

  @override
  String getTitleName() {
    return widget.pageType == ORDER_PAGE_TYPE_REFUND ? "退款单" : "订单";
  }

  Widget buildFilterButton(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        jumpFilterPage(context);
      },
      child: Container(
        height: 44,
        width: 44,
        alignment: Alignment.center,
        child: Image.asset(
          "assets/images/titlebar/icon_filter.png",
          width: 21,
          height: 21,
        ),
      ),
    );
  }

  void jumpFilterPage(BuildContext context) async {
    Map<String, String?> navigatorParams = Map.from(_listModel.filterParams);
    if (widget.pageType == ORDER_PAGE_TYPE_REFUND) {
      //退单状态
      navigatorParams["auditState"] = _statusModel.getOrderStatusStr();
    } else {
      //订单状态
      navigatorParams["statusStr"] = _statusModel.getOrderStatusStr();
    }

    Map<String, String?> filterParams = new Map<String, String?>();
    filterParams.addAll(navigatorParams);
    var result =
        await Navigator.pushNamed(context, "/order_filter_page", arguments: {
      'sourceJSON': json.encode(_statusModel.tagListModel),
      'isRefund': widget.pageType == ORDER_PAGE_TYPE_REFUND,
      'currentParams': filterParams,
    });
    if (result is Map) {
      Map asResult = result as Map<String, String?>;

      /// 如果不是关闭返回的 则处理参数
      if (!asResult.keys.contains("isClose")) {
        if (widget.pageType == ORDER_PAGE_TYPE_REFUND) {
          if (asResult.containsKey("auditState")) {
            _statusModel.updateStatusListByStr(
                asResult["auditState"]?.toString() ?? "");
            asResult.remove("auditState");
          } else {
            _statusModel.resetSelectedStatus();
            _statusModel.updateStatusUI();
          }
        } else {
          if (asResult.containsKey("statusStr")) {
            _statusModel
                .updateStatusListByStr(asResult["statusStr"]?.toString() ?? "");
            asResult.remove("statusStr");
          } else {
            _statusModel.resetSelectedStatus();
            _statusModel.updateStatusUI();
          }
        }
        this._listModel.filterParams = asResult as Map<String, String?>;
        Future.delayed(Duration(milliseconds: 100), () {
          _listModel.requestListModel(true, _statusModel.getOrderStatusStr());
        });
      }
    }
  }

  Widget buildSearchButton() {
    return GestureDetector(
      onTap: () {
        Map<String, String?> filterParams =
            this._listModel.buildParamsMap(_statusModel.getOrderStatusStr());
        Navigator.of(context).pushNamed("/order_search_page", arguments: {
          "pageType": widget.pageType,
          "filterParams": filterParams,
        });
      },
      child: Container(
        height: 44,
        width: 44,
        alignment: Alignment.center,
        child: Image.asset(
          "assets/images/titlebar/icon_search.png",
          width: 21,
          height: 21,
        ),
      ),
    );
  }

  _buildItemTag(int index) {
    OrderTagData item = _statusModel.statusList![index];
    var textColor;
    var bgColor;
    var borderColor;
    if (item.isSelect == true) {
      textColor = const Color(0xFF00B377);
      bgColor = const Color(0x1A00B377);
      borderColor = textColor;
    } else {
      textColor = const Color(0xFF292933);
      bgColor = const Color(0xFFF7F7F8);
      borderColor = bgColor;
    }

    return GestureDetector(
      onTap: () {
        handleTagClick(index);
      },
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            constraints: BoxConstraints(minWidth: 63),
            height: 32,
            padding: EdgeInsets.symmetric(horizontal: 11),
            decoration: BoxDecoration(
                color: bgColor, borderRadius: BorderRadius.circular(2)),
            alignment: Alignment.center,
            child: Text(
              item.value ?? "",
              style: TextStyle(
                  color: textColor,
                  fontSize: 14,
                  fontWeight: FontWeight.normal),
            ),
          ),
        ],
      ),
    );
  }

  buildOrderItem(int index) {
    if (_listModel.list == null || index >= (_listModel.list?.length ?? 0)) {
      return Container();
    }
    var itemData = _listModel.list![index];
    return GestureDetector(
      onTap: () {
        print("item111:${_listModel.pageType},${itemData.toJson()},${itemData.isParent}");
        if (_listModel.pageType == ORDER_PAGE_TYPE_REFUND) {
          print("itemData111: ${itemData.toJson()},${itemData.isParent}");
          Navigator.of(context).pushNamed("/OrderDetailRefundPage", arguments: {
            "orderId": itemData.id,
            "merchantId": itemData.merchantId,
            // "isParent":itemData.isParent //退单情况待定
          });
        } else {
          print("itemData: ${itemData.toJson()},${itemData.isParent}");
          Navigator.of(context).pushNamed("/order_detail_page", arguments: {
            "orderId": itemData.id,
            "merchantId": itemData.merchantId,
            "isParent":itemData.isParent
          });
        }
      },
      child: widget.pageType == ORDER_PAGE_TYPE_REFUND
          ? OrderRefundListItemWidget(_listModel.smallImageHost, itemData)
          : OrderListItemWidget(_listModel.smallImageHost, itemData),
    );
  }

  void handleTagClick(int index) {
    if (widget.pageType == ORDER_PAGE_TYPE_REFUND) {
      // 单选
      _statusModel.resetSelectedStatus();
      _statusModel.setSelectedStatusByIndex(index);
    } else {
      // 多选、反选
      if (index == 0) {
        _statusModel.resetSelectedStatus();
      } else {
        var clickItem = _statusModel.statusList![index];
        clickItem.isSelect = !clickItem.isSelect!;
        var selectedStatus = _statusModel.getSelectedStatus();
        if (selectedStatus.isEmpty) {
          _statusModel.resetSelectedStatus();
        } else if (selectedStatus.length >= 2 &&
            _statusModel.statusList?.first.isSelect == true) {
          selectedStatus.first.isSelect = false;
        }
      }
    }
    _statusModel.updateStatusUI();
    _listModel.requestListModel(true, _statusModel.getOrderStatusStr());
  }

  void requestStatusList(OrderListModel? listModel) {
    if (widget.pageType == ORDER_PAGE_TYPE_REFUND) {
      _statusModel.requestRefundStatusList(listModel);
    } else {
      _statusModel.requestStatusList(listModel);
    }
  }

  Widget? getEmptyWidget() {
    if (_statusModel.isSuccess == null || _listModel.isSuccess == null) {
      return null;
    }
    if (_statusModel.isSuccess == false || _listModel.isSuccess == false) {
      return PageStateWidget.pageEmpty(PageState.Error, errorClick: () {
        requestStatusList(_listModel);
      });
    }
    if ((_listModel.list?.length ?? 0) == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }
}

class OrderStatusModel extends ChangeNotifier {
  bool isExpand = false;
  List<OrderTagData>? statusList;
  String? widegtStatus; // 订单状态 字符串， 兼容首页跳转使用
  var _isDisposed = false;
  bool? isSuccess;

  OrderStatusModel();

  /// 筛选数据源
  OrderStatusTagData? tagListModel;

  List<String>? get widgetStatusIds {
    return widegtStatus?.split(',');
  }

  updateStatusUI() {
    notifyListeners();
  }

  toggleExpandStatus() {
    isExpand = !isExpand;
    notifyListeners();
  }

  updateStatusList(List<OrderTagData>? statusList) {
    this.statusList = statusList;

    /// 如果 没有外部传入的状态 则设置默认状态
    if (this.widegtStatus == null) {
      resetSelectedStatus();
    }
    notifyListeners();
  }

  resetSelectedStatus() {
    if (statusList != null && statusList!.isNotEmpty) {
      _clearSelectedStatus();
      statusList?.first.isSelect = true;
    }
  }

  _clearSelectedStatus() {
    statusList?.forEach((e) {
      e.isSelect = false;
    });
  }

  setSelectedStatusByIndex(int index) {
    _clearSelectedStatus();
    statusList![index].isSelect = true;
  }

  List<OrderTagData> getSelectedStatus() {
    return statusList?.where((element) => element.isSelect == true).toList() ??
        List.empty();
  }

  setSelectedStatus(OrderTagData orderTag) {
    _clearSelectedStatus();
    updateSingleStatus(orderTag, true);
  }

  updateStatusListByStr(String statusStr) {
    var splitList = statusStr.split(",");
    if (splitList.isEmpty) {
      resetSelectedStatus();
    } else {
      statusList?.forEach((element) {
        element.isSelect = splitList.contains(element.id.toString());
      });
    }
    notifyListeners();
  }

  updateSingleStatus(OrderTagData orderTag, bool isSelect) {
    statusList!.forEach((e) {
      if (e == orderTag) {
        e.isSelect = isSelect;
      }
    });
  }

  requestStatusList(OrderListModel? listModel) {
    EasyLoading.show(status: "加载中", maskType: EasyLoadingMaskType.clear);
    NetworkV2<OrderStatusTagData>(OrderStatusTagData())
        .requestDataV2("order/enumsForOrderSearch", method: RequestMethod.GET)
        .then((value) {
      EasyLoading.dismiss();
      if (!_isDisposed && value.isSuccess != null && value.isSuccess!) {
        isSuccess = value.isSuccess;
        List<OrderTagData>? orderStatuses = value.getData()?.orderStatuses;
        if (this.widegtStatus != null) {
          orderStatuses?.forEach((element) {
            if (this.widgetStatusIds?.contains('${element.id}') == true) {
              element.isSelect = true;
            } else {
              element.isSelect = false;
            }
          });
        }
        updateStatusList(orderStatuses);
        tagListModel = value.getData();
        if (statusList?.isNotEmpty == true) {
          listModel!.requestListModel(true, getOrderStatusStr());
        }
      }
    });
  }

  requestRefundStatusList(OrderListModel? listModel) {
    EasyLoading.show(status: "加载中", maskType: EasyLoadingMaskType.clear);
    NetworkV2<OrderStatusTagData>(OrderStatusTagData())
        .requestDataV2("refund/enumsForRefundSearch",
            contentType: RequestContentType.FORM, method: RequestMethod.POST)
        .then((value) {
      EasyLoading.dismiss();
      if (!_isDisposed && value.isSuccess == true) {
        isSuccess = value.isSuccess;
        updateStatusList(value.getData()?.orderRefundStatus);
        tagListModel = value.getData();
        if (statusList?.isNotEmpty == true) {
          listModel!.requestListModel(true, getOrderStatusStr());
        }
      }
    });
  }

  @override
  void dispose() {
    _isDisposed = true;
    super.dispose();
  }

  String getOrderStatusStr() {
    return getSelectedStatus().map((e) => e.id).join(",");
  }
}

class OrderListModel extends ChangeNotifier {
  var _isDisposed = false;
  bool? isLastPage = false;
  int pageNum = 0;
  String orderBranchCode = "-1";
  String sorted = "1";
  bool forceRefresh = false;
  List<OrderListItemData>? list;
  String? totalMoneyText;
  String? smallImageHost;
  String? tips;
  bool? isSuccess;

  final String? pageType; // 订单状态
  final String? dateType; // 时间类型
  final String? id; // 执行人id
  final String? isGroup; // 是否为组织

  Map<String, String?> filterParams = Map();

  final EasyRefreshController _refreshController;
  final ScrollController _scrollController;

  OrderListModel(this._scrollController, this._refreshController, this.pageType,
      this.dateType, this.id, this.isGroup);

  requestSmallImageHost() {
    if (smallImageHost == null) {
      XYYContainer.bridgeCall('app_host').then((value) {
        if (value is Map) {
          smallImageHost = value["image_host"];
          notifyListeners();
        }
      });
    }
  }

  void requestNormalList(Map<String, String?> paramsMap) {
    NetworkV2<OrderManageData>(OrderManageData())
        .requestDataV2("order/selectOrdersForOrderManagementNew",
            method: RequestMethod.GET, parameters: paramsMap)
        .then((value) {
      handleResult(value.isSuccess, value.getData());
    });
  }

  void requestTotalMoney(Map<String, String?> paramsMap) {
    NetworkV2<OrderManageData>(OrderManageData())
        .requestDataV2("order/getOrderTotalMoney",
            method: RequestMethod.GET, parameters: paramsMap)
        .then((value) {
      if (!_isDisposed && value.isSuccess == true) {
        this.tips = value.getData()?.tips;
        formatTotalMoney(value.getData()?.totalMoney ?? 0.0);
        notifyListeners();
      }
    });
  }

  void requestRefundList(Map<String, String?> paramsMap) {
    NetworkV2<OrderManageData>(OrderManageData())
        .requestDataV2("refund/queryRefundOrderPageNew",
            contentType: RequestContentType.FORM,
            method: RequestMethod.POST,
            parameters: paramsMap)
        .then((value) {
      if (value.isSuccess == true) {
        formatTotalMoney(value.getData()?.totalMoney ?? 0.0);
      }

      handleResult(value.isSuccess, value.getData());
    });
  }

  requestListModel(bool isRefresh, String orderStatusStr) async {
    if (isRefresh) {
      pageNum = 0;
      forceRefresh = true;
      list?.clear();
    } else {
      pageNum += 1;
      forceRefresh = false;
    }
    EasyLoading.show(status: "加载中", maskType: EasyLoadingMaskType.clear);
    var paramsMap = buildParamsMap(orderStatusStr);
    if (pageType == ORDER_PAGE_TYPE_REFUND) {
      requestRefundList(paramsMap);
    } else {
      requestNormalList(paramsMap);

      /// 如果是刷新则请求订单金额接口
      if (isRefresh) {
        requestTotalMoney(paramsMap);
      }
    }
  }

  void handleResult(bool? isSuccess, OrderManageData? data) {
    EasyLoading.dismiss();
    if (!_isDisposed && (isSuccess ?? false)) {
      this.isSuccess = isSuccess;
      if (data != null) {
        print(
            "guan $pageType lastPage ${data.page?.lastPage ?? false} ,${data.isLastPage}");
        var tempList;
        if (pageType == ORDER_PAGE_TYPE_REFUND) {
          isLastPage = data.page?.isLastPage;
          tempList = data.page?.list;
        } else {
          isLastPage = data.page?.isLastPage;
          tempList = data.page?.list;
        }
        if (forceRefresh) {
          list = tempList;
          _scrollController.jumpTo(0);
        } else {
          if (list == null) {
            list = tempList;
          } else {
            list?.addAll(tempList ?? []);
          }
        }
      } else {
        isLastPage = true;
      }
      _refreshController.finishRefresh();
      _refreshController.finishLoad(noMore: isLastPage ?? true);
      notifyListeners();
    } else {
      _refreshController.finishRefresh();
      _refreshController.finishLoad(noMore: false);
    }
  }

  Map<String, String?> buildParamsMap(String orderStatusStr) {
    var params = Map<String, String?>();
    //分页参数
    params["offset"] = pageNum.toString();
    params["limit"] = "10";
    //查询范围
    if (!filterParams.containsKey("groupId") &&
        !filterParams.containsKey("searchUserId") &&
        id != null &&
        id!.isNotEmpty) {
      //筛选参数里没有这两个，则使用外面传入的
      params[isGroup == "true" ? "groupId" : "searchUserId"] = id;
    }
    if (pageType == ORDER_PAGE_TYPE_REFUND) {
      //日期
      if (filterParams.containsKey("refundPeriod")) {
        // 包含退款审核时间 就不传递订单日期
        params['refundPeriod'] = filterParams['refundPeriod'];
      } else {
        //筛选页面可能会覆盖这个参数，默认为1（今日）
        params["period"] = dateType?.toString() ?? "1";
      }
      //订单状态
      params["auditState"] = orderStatusStr;
    } else {
      //日期
      params["sceneType"] = dateType?.toString() ?? "1";

      //退单状态
      params["statusStr"] = orderStatusStr;

      // 排序
      if (!filterParams.containsKey("sorted")) {
        params["sorted"] = "1";
      } else {
        params["sorted"] = filterParams["sorted"];
      }
    }
    params["orderBranchCode"] = orderBranchCode;
    params.addAll(filterParams);
    if (params.containsKey("productTypes") &&
        params['productTypes']?.toString() == "-1") {
      params.remove("productTypes");
    }
    if (params.containsKey("payChannels") &&
        params["payChannels"]?.toString() == "-1") {
      params.remove("payChannels");
    }
    if (params.containsKey("payTypes") &&
        params["payTypes"]?.toString() == "-1") {
      params.remove("payTypes");
    }
    if (params.containsKey("yhType") && params["yhType"]?.toString() == "-1") {
      params.remove("yhType");
    }
    if (params.containsKey("refundChannelStr") &&
        params["refundChannelStr"]?.toString() == "-1") {
      params.remove("refundChannelStr");
    }
    if (params.containsKey("storeStatusStr") &&
        params["storeStatusStr"]?.toString() == "-1") {
      params.remove("storeStatusStr");
    }
    if (params.containsKey("auditState") &&
        params["auditState"]?.toString() == "-1") {
      params.remove("auditState");
    }
    if (params.containsKey("statusStr") &&
        params["statusStr"]?.toString() == "-1") {
      params.remove("statusStr");
    }
    // 卡单状态
    if (params.containsKey("exceptionTypes") &&
        params["exceptionTypes"]?.toString() == "-2") {
      params.remove("exceptionTypes");
    }
    //一段老逻辑，意义不明
    // 原注释：当channelType是订单管理时，需要去掉多余的branchCode参数，但是首页的筛选还需要保留，所以只更改订单的parameterMap
    if (pageType == ORDER_PAGE_TYPE_MINE) {
      params.remove("branchCode");
    }
    return params;
  }

  @override
  void dispose() {
    _isDisposed = true;
    super.dispose();
  }

  void formatTotalMoney(double totalMoney) {
    var format = NumberFormat("#,##0.00", "en_US");
    totalMoneyText = format.format(totalMoney);
  }
}
