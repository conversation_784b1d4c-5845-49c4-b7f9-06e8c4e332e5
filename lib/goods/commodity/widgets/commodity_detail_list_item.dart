import 'dart:async';
import 'dart:ui';

import 'package:XyyBeanSproutsFlutter/common/image/image_catch_widget.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/data/commodity_rank_data.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:collection/collection.dart';

class CommodityDetailListItem extends StatelessWidget {
  final Key? key;
  final bool isMerchantEnter;

  final CommodityDetailGoodItemModel model;

  ///用于埋点商卡曝光
  final VoidCallback? onRendered; // 非必填：默认null，加?表示可空

  CommodityDetailListItem({ this.key,this.isMerchantEnter = false, required this.model,this.onRendered,}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 当Item渲染完成后，触发回调（传递自身高度）
    WidgetsBinding.instance?.addPostFrameCallback((_) {
      // 通过自身Key获取高度
      final renderBox = key is GlobalKey
          ? (key as GlobalKey).currentContext?.findRenderObject() as RenderBox?
          : null;
      final height = renderBox?.size.height ?? 0;
      onRendered?.call(); // 通知外部已渲染
    });
    return Container(
      color: Color(0xFFF1F6F9),
      padding: EdgeInsets.fromLTRB(10, 10, 10, 0),
      child: Container(
        decoration: BoxDecoration(
          color: Color(0xFFFFFFFF),
          borderRadius: BorderRadius.circular(8),
        ),
        padding: EdgeInsets.only(bottom: 10),
        width: double.infinity,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            this.goodImageWidget(),
            Expanded(child: this.getContentWidget()),
          ],
        ),
      ),
    );
  }

  /// 商品图片
  Widget goodImageWidget() {
    return Container(
      height: 80,
      width: 83,
      child: Stack(
        children: [
          // 图片
          Positioned(
            left: 13,
            top: 10,
            child: Container(
              color: Color(0xFFFFFFFF),
              child: ImageCatchWidget(
                url: model.skuImageUrl,
                w: 70,
                h: 70,
              ),
            ),
          ),
          // 售罄或无购买权限标识
          Visibility(
            visible: getStockOrPermissionText().isNotEmpty,
            child: Positioned(
              bottom: 0,
              left: 13,
              right: 0,
              child: Container(
                decoration: BoxDecoration(
                  color: const Color(0xFFF1F6F9),
                  borderRadius: BorderRadius.vertical(top: Radius.circular(8)),
                ),
                padding: EdgeInsets.symmetric(vertical: 2),
                alignment: Alignment.center,
                child: Text(
                  getStockOrPermissionText(),
                  style: TextStyle(color: Color(0xFF9494A6), fontSize: 11, fontWeight: FontWeight.w500),
                ),
              ),
            ),
          ),
          // 控销 优选  标签
          getGoodsTagWidget(),
          //甄选 标签
          // Visibility(
          //   visible: "${model.isHighGross}" == "3",
          //   child: Positioned(
          //     top: -10,
          //     left: 0,
          //     child: Image.asset(
          //       'assets/images/commodity/isHighGrossIcon.jpg',
          //       width: 30,
          //       height: 40,
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }

  /// 返回购买权限标识或者售罄标识，售罄表示优先展示
  String getStockOrPermissionText() {
    if (model.skuSellOut?.toString() == "1") {
      // 有库存
      if (!isMerchantEnter || model.skuBuyLimit?.toString() == "1") {
        //可以购买
        return "";
      } else {
        // 这里要判断从客户详情进展示
        return "无购买权限";
      }
    } else {
      // 无库存
      return "售罄";
    }
  }

  /// 控销 优选 标签
  Widget getGoodsTagWidget() {
    return Positioned(
      left: 10,
      top: 10,
      child: Row(
        children: [
          Visibility(
            visible: "${model.skuCollectType}" == "2",
            child: Transform.scale(
              scale: 1.3,
              child: Image.asset(
                'assets/images/order/order_detail_control_icon.png',
                width: 35,
                height: 13,
              ),
            ),
          ),
          Visibility(
            visible: "${model.skuCollectType}" == "2", //优选
            child: SizedBox(width: 2),
          ),
          Visibility(
            visible: "${model.isHighGross}" == "2",
            child: Transform.scale(
              scale: 1.3,
              child: Image.asset(
                'assets/images/order/order_detail_height_icon.png',
                width: 35,
                height: 13,
              ),
            ),
          ),
          Visibility(
            visible: "${model.isHighGross}" == "3",//甄选
            child: Transform.scale(
              scale: 1.3,
              child: Image.asset(
                'assets/images/commodity/isHighGrossIcon.jpg',
                width: 35,
                height: 13,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 商品信息部分
  Widget getContentWidget() {
    return Container(
      padding: EdgeInsets.fromLTRB(10, 10, 10, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 药品名称
          Text('${model.skuName}', style: TextStyle(color: Color(0xFF292933), fontSize: 16, fontWeight: FontWeight.w500)),
          SizedBox(height: 2),
          // 规格
          Text('规格：${model.skuSpec}', style: TextStyle(color: Color(0xFF676773), fontSize: 12)),
          SizedBox(height: 2),
          // 生产厂家
          Text('厂家：${model.manufacturer}', style: TextStyle(color: Color(0xFF676773), fontSize: 12)),
          SizedBox(height: 2),
          // 有效期
          Text('有效期：${model.effectTime}', style: TextStyle(color: Color(0xFF676773), fontSize: 12)),
          SizedBox(height: 2),
          // 普通商品价格部分
          Visibility(
            visible: !model.isSpellGroup,
            child: getPricePartWidget(),
          ),
          // 拼团商品价格部分
          Visibility(
            visible: model.isSpellGroup,
            child: getSpellGroupPart(),
          )
        ],
      ),
    );
  }

  /// 价格部分
  Widget getPricePartWidget() {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 售价+终端毛利率
              getSalesPriceWidget(),
              // 预估到手价 （仅在客户入口下展示）
              getEstimatePrice(),
              // 标签
              getDiscountWidget(),
              // 店铺
              getShopWidget(),
            ],
          ),
        ),
        // 平均到手价
        getAverPriceWidget(),
      ],
    );
  }

  /// 商品售价
  Widget getSalesPriceWidget() {
    return RichText(
      text: TextSpan(
        text: "¥",
        style: TextStyle(color: Color(0xFF292933), fontSize: 10, fontWeight: FontWeight.w500),
        children: [
          TextSpan(
            text: getSalesPriceIntegerPart(),
            style: TextStyle(color: Color(0xFF292933), fontSize: 18, fontWeight: FontWeight.w600),
          ),
          TextSpan(
            text: getSalesPriceDecimalPart(),
            style: TextStyle(color: Color(0xFF292933), fontSize: 11, fontWeight: FontWeight.w600),
          ),
          TextSpan(
            text: "(终端毛利率${model.skuGrossMargin})",
            style: TextStyle(color: Color(0xFF676773), fontSize: 11),
          ),
        ],
      ),
    );
  }

  /// 售价 整数部分
  String getSalesPriceIntegerPart() {
    dynamic price = "${model.skuPrice}";
    if (price == null) {
      return "";
    }
    if (!price.toString().contains(".")) {
      return price.toString();
    } else {
      return price.toString().split(".")[0];
    }
  }

  /// 售价 小数部分
  String getSalesPriceDecimalPart() {
    dynamic price = "${model.skuPrice}";
    if (price == null) {
      return "";
    }
    if (!price.toString().contains(".")) {
      return "";
    } else {
      return "." + price.toString().split(".")[1];
    }
  }

  /// 优惠信息
  Widget getDiscountWidget() {
    List<Widget> childs = [];

    model.tagList?.forEach((element) {
      if ("${element.uiType}" == "1") {
        /// 临期
        childs.add(
          Container(
            decoration:
                BoxDecoration(color: Color(0x0DF78D00), borderRadius: BorderRadius.circular(1), border: Border.all(color: Color(0x80FF7200), width: 0.5)),
            padding: EdgeInsets.only(left: 3, right: 3),
            height: 15,
            child: Text(
              '${element.name}',
              style: TextStyle(color: Color(0xFFFF7200), fontSize: 10),
            ),
          ),
        );
        childs.add(SizedBox(width: 2.5));
      } else if ("${element.uiType}" == "2") {
        if ("${element.uiStyle}" == "2") {
          /// 优惠券
          childs.add(SizedBox(
            height: 15,
            child: CustomPaint(
              painter: _TagCouponsBase(),
              child: Container(
                padding: EdgeInsets.only(left: 4, right: 4),
                child: Text(
                  '${element.name}',
                  style: TextStyle(color: Color(0xFFFF2121), fontSize: 10),
                ),
              ),
            ),
          ));
          childs.add(SizedBox(width: 2.5));
        } else {
          /// 满减
          childs.add(
            Container(
              decoration:
                  BoxDecoration(color: Color(0x0DFF2121), borderRadius: BorderRadius.circular(1), border: Border.all(color: Color(0xFFFF2121), width: 0.5)),
              padding: EdgeInsets.only(left: 3, right: 3),
              height: 15,
              child: Text(
                '${element.name}',
                style: TextStyle(color: Color(0xFFFF2121), fontSize: 10),
              ),
            ),
          );
          childs.add(SizedBox(width: 2.5));
        }
      }
    });

    return Visibility(
      visible: childs.isNotEmpty,
      child: Padding(
        padding: const EdgeInsets.only(top: 2),
        child: Wrap(
          spacing: 2.5, // 水平方向标签间距
          runSpacing: 4, // 垂直方向行间距
          children: childs,
        ),
      ),
    );
  }

  /// 预估到手价
  Widget getEstimatePrice() {
    return Visibility(
      visible: this.isMerchantEnter && model.discountPrice != null,
      child: Padding(
        padding: const EdgeInsets.only(top: 2),
        child: Text(
          '${model.discountPrice}',
          style: TextStyle(color: Color(0xFFFF2121), fontSize: 11, fontWeight: FontWeight.w500),
        ),
      ),
    );
  }

  /// 平均到手价
  Widget getAverPriceWidget() {
    return Visibility(
      visible: !this.isMerchantEnter /* && (itemData.skuAverPrice != null)*/,
      child: Container(
        padding: EdgeInsets.all(4),
        decoration: BoxDecoration(
          color: Color(0xFFFFFBF8),
          border: Border.all(
            width: 0.5,
            color: Color(0xFFFDAC77),
          ),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text.rich(
              TextSpan(
                text: this.noAverPrice() ? "" : "¥",
                style: TextStyle(
                  color: Color(0xFFFC6B0B),
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                ),
                children: [
                  TextSpan(
                    text: this.getAverPriceIntegerPart(),
                    style: TextStyle(
                      color: Color(0xFFFC6B0B),
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  TextSpan(
                    text: this.getAverPriceDecimalPart(),
                    style: TextStyle(
                      color: Color(0xFFFC6B0B),
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            Text(
              "平均到手价",
              style: TextStyle(
                color: Color(0xFFFC6B0B),
                fontSize: 10,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 平均到手价 整数部分
  String getAverPriceIntegerPart() {
    dynamic price = "${model.skuAverPrice}";
    if (this.noAverPrice()) {
      return "--";
    }
    if (!price.toString().contains(".")) {
      return price.toString();
    } else {
      return price.toString().split(".")[0];
    }
  }

  /// 平均到手价 小数部分
  String getAverPriceDecimalPart() {
    dynamic price = "${model.skuAverPrice}";
    if (this.noAverPrice()) {
      return "";
    }
    if (!price.toString().contains(".")) {
      return "";
    } else {
      return "." + price.toString().split(".")[1];
    }
  }

  bool noAverPrice() {
    dynamic price = "${model.skuAverPrice}";
    return price == null || price == "0.0" || price == "0" || price == "0.00" || price == "--";
  }

  /// 拼团信息部分
  Widget getSpellGroupPart() {
    return Column(
      children: [
        // 拼团价格
        Row(
          children: [
            Text(
              '拼团价¥',
              style: TextStyle(color: Color(0xFFFF2121), fontSize: 11, fontWeight: FontWeight.w500),
            ),
            getSpellGroupPriceWidget(),
            Spacer(),
            Text(
              '${model.ptActivityInfo?.leastPurchaseNum ?? "--"}起拼',
              style: TextStyle(color: Color(0xFF292933), fontSize: 11, fontWeight: FontWeight.w500),
            )
          ],
        ),
        // 倒计时
        CommoditySpellGroupCoundownWidget(countdown: model.ptActivityInfo?.realCountDown),
        // 店铺
        this.getShopWidget(),
      ],
    );
  }

  /// 拼团价格
  Widget getSpellGroupPriceWidget() {
    String price = "${model.ptActivityInfo?.priceInfo ?? ""}".replaceAll("¥", "");
    List<String> priceList = price.split('-');
    if (priceList.length > 1) {
      priceList.last = "-" + priceList.last;
    }
    List<String> partForPrices = [];
    priceList.forEachIndexed((index, element) {
      List<String> pricePart = element.split('.');
      if (pricePart.length > 1) {
        pricePart.last = "." + pricePart.last;
      }
      partForPrices.addAll(pricePart);
    });

    List<TextSpan> spans = [];
    if (partForPrices.length > 0) {
      spans = partForPrices
          .getRange(1, partForPrices.length)
          .mapIndexed(
            (index, element) =>
                TextSpan(text: element, style: TextStyle(color: Color(0xFFFF2121), fontSize: index % 2 == 0 ? 11 : 17, fontWeight: FontWeight.w600)),
          )
          .toList();
    }

    if (spans.length > 0) {
      return RichText(
        text: TextSpan(
          text: partForPrices.first,
          style: TextStyle(color: Color(0xFFFF2121), fontSize: 17, fontWeight: FontWeight.w600),
          children: spans,
        ),
      );
    }
    return Container();
  }

  /// 店铺
  Widget getShopWidget() {
    return Container(
      padding: EdgeInsets.only(top: 2),
      child: Row(
        children: [
          Image.asset(
            'assets/images/commodity/commodity_shop_icon.png',
            width: 12,
            height: 11,
          ),
          SizedBox(width: 3),
          Expanded(
            child: Text(
              '${model.shopName ?? "--"}',
              style: TextStyle(color: Color(0xFF676773), fontSize: 11),
              overflow: TextOverflow.ellipsis,
            ),
          )
        ],
      ),
    );
  }
}

class CommoditySpellGroupCoundownWidget extends StatefulWidget {
  final dynamic countdown;

  CommoditySpellGroupCoundownWidget({this.countdown});

  @override
  State<StatefulWidget> createState() {
    return CommoditySpellGroupCoundownState();
  }
}

class CommoditySpellGroupCoundownState extends State<CommoditySpellGroupCoundownWidget> with AutomaticKeepAliveClientMixin {
  late ValueNotifier<int> controller = ValueNotifier(0);
  late Timer _timer;

  @override
  void initState() {
    int countdown = int.tryParse("${widget.countdown}") ?? 0;
    this.controller.value = countdown;

    _timer = Timer.periodic(Duration(seconds: 1), (t) {
      if (this.controller.value > 0) {
        this.controller.value = this.controller.value - 1;
      }
      if (this.controller.value <= 0) {
        t.cancel();
      }
    });
    super.initState();
  }

  @override
  void dispose() {
    if (this._timer.isActive) {
      this._timer.cancel();
    }
    this.controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return ValueListenableBuilder<int>(
      valueListenable: this.controller,
      builder: (context, value, child) {
        String day = "${value ~/ 86400}".padLeft(2, '0');
        String hours = "${value % 86400 ~/ 3600}".padLeft(2, '0');
        String mintues = "${value % 3600 ~/ 60}".padLeft(2, '0');
        String seconds = "${value % 60}".padLeft(2, '0');
        return CommoditySpellGroupimeWidget(
          day: day,
          hours: hours,
          minutes: mintues,
          seconds: seconds,
        );
      },
    );
  }

  @override
  bool get wantKeepAlive => true;
}

class CommoditySpellGroupimeWidget extends StatelessWidget {
  final String day;
  final String hours;
  final String minutes;
  final String seconds;

  CommoditySpellGroupimeWidget({
    this.day = "0",
    this.hours = "0",
    this.minutes = "0",
    this.seconds = "0",
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Row(
        children: [
          Text(
            '拼团仅剩：',
            style: TextStyle(color: Color(0xFF676773), fontSize: 11),
          ),
          Container(
            decoration: BoxDecoration(
              color: Color(0xFFF5F8FA),
              borderRadius: BorderRadius.circular(1),
            ),
            padding: EdgeInsets.all(1),
            child: Text(
              this.day,
              style: TextStyle(
                color: Color(0xFF191919),
                fontSize: 10,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Text(
            '天',
            style: TextStyle(color: Color(0xFF191919), fontSize: 11),
          ),
          Container(
            decoration: BoxDecoration(
              color: Color(0xFFF5F8FA),
              borderRadius: BorderRadius.circular(1),
            ),
            padding: EdgeInsets.all(1),
            child: Text(
              this.hours,
              style: TextStyle(
                color: Color(0xFF191919),
                fontSize: 10,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Text(
            ':',
            style: TextStyle(color: Color(0xFF191919), fontSize: 11),
          ),
          Container(
            decoration: BoxDecoration(
              color: Color(0xFFF5F8FA),
              borderRadius: BorderRadius.circular(1),
            ),
            padding: EdgeInsets.all(1),
            child: Text(
              this.minutes,
              style: TextStyle(
                color: Color(0xFF191919),
                fontSize: 10,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Text(
            ':',
            style: TextStyle(color: Color(0xFF191919), fontSize: 11),
          ),
          Container(
            decoration: BoxDecoration(
              color: Color(0xFFF5F8FA),
              borderRadius: BorderRadius.circular(1),
            ),
            padding: EdgeInsets.all(1),
            child: Text(
              this.seconds,
              style: TextStyle(
                color: Color(0xFF191919),
                fontSize: 10,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _TagCouponsBase extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    var paint = Paint();
    paint.color = Color(0xFFFDF8F9);
    paint.style = PaintingStyle.fill;
    paint.strokeWidth = 0.5;

    double arcRadus = 3;
    double offset = (size.height - arcRadus * 2) / 2;

    Path path = Path()
      ..moveTo(0, 0)
      ..lineTo(size.width, 0)
      ..lineTo(size.width, offset)
      ..arcToPoint(Offset(size.width, size.height - offset), radius: Radius.circular(3), clockwise: false)
      ..lineTo(size.width, size.height)
      ..lineTo(0, size.height)
      ..lineTo(0, size.height - offset)
      ..arcToPoint(Offset(0, offset), radius: Radius.circular(3), clockwise: false)
      ..close();

    canvas.drawPath(path, paint);

    paint.color = Color(0xFFE8949D);
    paint.style = PaintingStyle.stroke;
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}
