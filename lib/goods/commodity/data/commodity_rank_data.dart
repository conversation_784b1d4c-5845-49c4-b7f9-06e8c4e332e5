import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:intl/intl.dart';
import 'package:json_annotation/json_annotation.dart';

part 'commodity_rank_data.g.dart';

@JsonSerializable()
class CommodityRankData extends BaseModelV2<CommodityRankData> {
  dynamic? skuId; // 商品Id
  dynamic searchSkuId; // 查询子页面使用
  dynamic searchType; // 1:标准库ID 2:pid
  dynamic? skuImageUrl; // 商品图片
  dynamic? skuName; // 商品名称
  dynamic? skuNum; // 销量
  dynamic? skuSpec; // 商品规格
  dynamic? skuGrossMargin; // 商品毛利
  dynamic? skuAverPrice; // 到手平均价
  dynamic? skuLastPrice; // 预估到手价
  dynamic? skuSellOut; // 售罄标识；0-已售罄；1-有库存
  dynamic? shopName; // 商品所在店铺
  dynamic? rank; // 排行
  dynamic? skuPrice; // 商品价格
  dynamic? shopPropertyName; // 自营/非自营

  /// 下单客户数
  dynamic customerNum;

  /// 客户详情下专属字段
  dynamic? lastPurchaseDate; // 最后一次采购时间
  dynamic? skuBuyLimit; // 0-不能购买；1-可以购买

  CommodityRankData();

  bool isSelfShop() {
    return shopPropertyName == "自营";
  }

  bool hasPurchased() {
    if (lastPurchaseDate == null) {
      return false;
    }
    if (lastPurchaseDate.toString().isEmpty) {
      return false;
    }
    return true;
  }

  int? getRank() {
    return int.tryParse(rank?.toString() ?? "");
  }

  String getFormatRank() {
    var numberFormat = new NumberFormat("00");
    var rank = getRank();
    if (rank == null) {
      return "--";
    } else {
      return numberFormat.format(rank);
    }
  }

  String getPriceIntegerPart() {
    if (skuPrice == null) {
      return "-";
    }
    if (!skuPrice.toString().contains(".")) {
      return skuPrice.toString();
    } else {
      return skuPrice.toString().split(".")[0];
    }
  }

  String getPriceDecimalPart() {
    if (skuPrice == null) {
      return "-";
    }
    if (!skuPrice.toString().contains(".")) {
      return "";
    } else {
      return "." + skuPrice.toString().split(".")[1];
    }
  }

  factory CommodityRankData.fromJson(Map<String, dynamic> json) =>
      _$CommodityRankDataFromJson(json);

  @override
  CommodityRankData fromJsonMap(Map<String, dynamic> json) {
    return _$CommodityRankDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CommodityRankDataToJson(this);
  }
}

@JsonSerializable()
class CommodityDetailGoodItemModel extends BaseModelV2 {
  dynamic skuId;
  dynamic pSkuId;
  dynamic mSkuId;
  dynamic skuImageUrl;
  dynamic skuName;
  dynamic skuSpec;

  /// 生产厂家
  dynamic manufacturer;

  dynamic shopName;

  /// 售罄标签
  dynamic skuSellOut;

  /// 0-不能购买；1-可以购买
  dynamic skuBuyLimit;

  // 效期(近效期/远效期)
  dynamic effectTime;

  /// 售价
  dynamic skuPrice;

  /// 毛利率
  dynamic skuGrossMargin;

  /// 预估到手价
  dynamic discountPrice;

  /// 平均到手价
  dynamic skuAverPrice;

  /// 类型 1普药 2控销
  dynamic skuCollectType;

  /// 1-非优选 2-优选 3-甄选
  dynamic isHighGross;

  ///  3拼团
  dynamic productType;

  ///埋点qt_sku_data
  dynamic qt_sku_data;

  /// 是否是拼团数据
  bool get isSpellGroup => "$productType" == "3" && this.ptActivityInfo != null;

  CommodityDetailActivityModel? ptActivityInfo;

  List<CommodityDetailTagModel>? tagList;

  CommodityDetailGoodItemModel();

  factory CommodityDetailGoodItemModel.fromJson(Map<String, dynamic> json) =>
      _$CommodityDetailGoodItemModelFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$CommodityDetailGoodItemModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CommodityDetailGoodItemModelToJson(this);
  }
}

@JsonSerializable()
class CommodityDetailActivityModel extends BaseModelV2 {
  /// 拼团起购数量
  dynamic leastPurchaseNum;

  /// 拼团活动仅剩时间
  dynamic countdown;

  /// 拼团价/拼团阶梯价
  dynamic priceInfo;

  /// 请求结束后的时间戳 - 用来计算倒计时时间
  dynamic timeStamp;

  /// 获取真实计时时间
  int get realCountDown {
    int countdown = int.tryParse("${this.countdown}") ?? 0;
    int curTimeStamp = DateTime.now().millisecondsSinceEpoch;
    int requestTime = int.tryParse("${this.timeStamp}") ?? 0;

    int diffTime = curTimeStamp - requestTime;

    return (countdown - diffTime) ~/ 1000;
  }

  CommodityDetailActivityModel();

  factory CommodityDetailActivityModel.fromJson(Map<String, dynamic> json) =>
      _$CommodityDetailActivityModelFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$CommodityDetailActivityModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CommodityDetailActivityModelToJson(this);
  }
}

@JsonSerializable()
class CommodityDetailTagModel extends BaseModelV2 {
  /// 标签内容
  dynamic name;

  /// 标签类型 1-临期  2-券，满减等
  dynamic uiType;

  /// ui类型 2-券
  dynamic uiStyle;

  CommodityDetailTagModel();

  factory CommodityDetailTagModel.fromJson(Map<String, dynamic> json) =>
      _$CommodityDetailTagModelFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$CommodityDetailTagModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CommodityDetailTagModelToJson(this);
  }
}
