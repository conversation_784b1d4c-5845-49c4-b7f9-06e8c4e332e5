import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/button/dropdown_controller_button.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/customer/page/ybm_customer_search_history.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/page/hy_customer_private_page.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/page/ybm_customer_private_page.dart';
import 'package:XyyBeanSproutsFlutter/customer/public/page/hy_customer_public_page.dart';
import 'package:XyyBeanSproutsFlutter/customer/public/page/ybm_customer_public_page.dart';
import 'package:XyyBeanSproutsFlutter/customer/widget/hy_customer_search_popover.dart';
import 'package:XyyBeanSproutsFlutter/goods/control/page/control_goods_result.dart';
import 'package:XyyBeanSproutsFlutter/goods/widgets/tab_custom_indicator.dart';
import 'package:flutter/material.dart';

class CustomerSearchTabPage extends BasePage {
  final dynamic roleCode;
  final dynamic selectIndex;
  final dynamic keyword;

  CustomerSearchTabPage({
    this.keyword,
    required this.roleCode,
    required this.selectIndex,
  });

  @override
  BaseState<StatefulWidget> initState() {
    return CustomerSearchTabPageState();
  }
}

class CustomerSearchTabPageState extends BaseState<CustomerSearchTabPage>
    with SingleTickerProviderStateMixin {
  // Tab标题数组
  List<String> titles = ["私海客户", "公海客户"];

  FocusNode _focusNode = FocusNode();
  TextEditingController _textController = TextEditingController();

  ValueNotifier<String?> privateNotifier = ValueNotifier('');
  ValueNotifier<String?> publicNotifier = ValueNotifier('');

  // 荷叶搜索类型控制器 0 - POI信息, 1 - 荷叶信息
  ValueNotifier<int> hySearchTypeNotifier = ValueNotifier(0);

  // 控制输入框的清空按钮
  ValueNotifier<bool> textClearShow = ValueNotifier(false);

  // Tab控制器
  late TabController _controller;

  // 判断当前是否荷叶权限
  bool get isHY => '${widget.roleCode}' == "3";

  @override
  void initState() {
    this.initalTabController();
    if (widget.keyword != null) {
      this._textController.text = widget.keyword ?? "";
      this.textClearShow.value =
          this.isHY ? this._textController.text.isNotEmpty : false;

      /// 延迟100毫秒
      Future.delayed(Duration(milliseconds: 100), () {
        this.searchAction(widget.keyword ?? "");
      });
    }
    super.initState();
  }

  // 初始化Tab 控制器
  void initalTabController() {
    int initialIndex = int.tryParse("${widget.selectIndex ?? 0}") ?? 0;
    this._controller = TabController(
      length: this.titles.length,
      initialIndex: initialIndex,
      vsync: this,
    );
    this._controller.addListener(() {
      if (this._controller.index == 0) {
        this._textController.text = this.privateNotifier.value ?? '';
      } else {
        this._textController.text = this.publicNotifier.value ?? '';
      }
      this.textClearShow.value =
          this.isHY ? this._textController.text.isNotEmpty : false;
    });
  }

  @override
  void dispose() {
    this._controller.dispose();
    this._focusNode.dispose();
    this._textController.dispose();
    this.privateNotifier.dispose();
    this.publicNotifier.dispose();
    this.hySearchTypeNotifier.dispose();
    this.textClearShow.dispose();
    super.dispose();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      child: Column(
        children: [
          SizedBox(
            height: 44,
            child: TabBar(
              controller: this._controller,
              isScrollable: false,
              indicator: TabCustomIndicator(
                wantWidth: 28,
              ),
              indicatorSize: TabBarIndicatorSize.label,
              indicatorColor: Color(0xFF00B377),
              indicatorWeight: 3,
              unselectedLabelColor: Color(0xFF666666),
              unselectedLabelStyle: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w500,
              ),
              labelColor: Color(0xFF333333),
              labelStyle: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w600,
              ),
              tabs: tabs(),
            ),
          ),
          Divider(color: Color(0xFFF6F6F6), height: 0.5, thickness: 1),
          Expanded(
            child: TabBarView(
              controller: this._controller,
              children: this.tabViews(),
            ),
          )
        ],
      ),
    );
  }

  // Tab Item
  List<Widget> tabs() {
    return this
        .titles
        .map((e) => Container(
              height: 28,
              child: Tab(
                text: e,
              ),
            ))
        .toList();
  }

  // TabView Item
  List<Widget> tabViews() {
    if (this.isHY) {
      // 荷叶
      return [
        HYCustomerPrivatePage(
          searchChangeText: this.privateNotifier,
          searchType: this.hySearchTypeNotifier,
        ),
        HYCustomerPublicPage(
          searchChangeText: this.publicNotifier,
          searchType: this.hySearchTypeNotifier,
        ),
      ];
    } else {
      // 药帮忙
      return [
        YBMCustomerPrivatePage(
          searchChangeText: this.privateNotifier,
        ),
        YBMCustomerPublicPage(
          searchChangeText: this.publicNotifier,notQT:1

        ),
      ];
    }
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(
      this.getTitleName(),
      leftType: this.isHY ? LeftButtonType.custom : LeftButtonType.none,
      leadingWidth: this.isHY ? 120 : 10,
      leftButton: this.leftButton(),
      titleWidget: this.titleWidget(),
      rightButtons: [
        GestureDetector(
          onTap: () {
            Navigator.of(context).pop();
          },
          child: Container(
            width: 62,
            alignment: Alignment.center,
            child: Text(
              '取消',
              style: TextStyle(color: Color(0xFF35C561), fontSize: 14),
            ),
          ),
        )
      ],
    );
  }

  Widget leftButton() {
    GlobalKey authKey = GlobalKey();
    return Visibility(
      visible: this.isHY,
      child: Container(
        key: authKey,
        child: DropControllerButton(
          title: 'POI信息',
          normalStyle: TextStyle(
            color: Color(0xFF333333),
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
          selectedStyle: TextStyle(
            color: Color(0xFF333333),
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
          onPressed: (controller) async {
            var result = await showHYSearchSelectPopover<String>(
              context: context,
              anchorKey: authKey,
              models: ['POI信息', '荷叶信息'],
            );
            if (result == 'POI信息') {
              hySearchTypeNotifier.value = 0;
            } else {
              hySearchTypeNotifier.value = 1;
            }
            controller.setSelectText(result);
            controller.setIsOpen(false);
          },
        ),
      ),
    );
  }

  Widget titleWidget() {
    return Container(
      padding: EdgeInsets.only(left: 10),
      decoration: BoxDecoration(
        color: Color(0xFFF5F5F5),
        borderRadius: BorderRadius.all(Radius.circular(22)),
      ),
      child: Row(
        children: [
          SizedBox(
            width: 22,
            height: 22,
            child: Image.asset("assets/images/titlebar/icon_search_bar.png"),
          ),
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(left: 3),
              child: ValueListenableBuilder<int>(
                valueListenable: this.hySearchTypeNotifier,
                builder: (context, value, child) {
                  String hintText = "请输入客户名称/手机号/编号";
                  if ("${widget.roleCode}" == "1") {
                    hintText = "请输入客户名称/手机号/编号";
                  } else {
                    hintText = value == 0 ? "客户名称/手机号/编号" : '荷叶健康的门店ID编号';
                  }
                  return TextField(
                    autofocus: this.isHY ? true : false,
                    focusNode: _focusNode,
                    controller: _textController,
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      isDense: true,
                      hintText: hintText,
                      hintStyle: TextStyle(
                        fontSize: 14,
                        color: Color(0xFFb3b3c2),
                      ),
                    ),
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFF292933),
                    ),
                    textInputAction: TextInputAction.search,
                    onTap: () async {
                      if (!this.isHY) {
                        this._focusNode.unfocus();
                        dynamic result = await Navigator.of(context)
                            .push(CustomSearchResultRoute(
                          builder: (context) => YBMCustomerSearchHistoryPage(
                            searchType: this._controller.index,
                            keyword: this._textController.text,
                          ),
                        ));
                        if (result != null) {
                          String keyword = "$result";
                          this._textController.text = keyword;
                          this.searchAction(keyword);
                        }
                      }
                    },
                    onSubmitted: this.searchAction,
                    onChanged: (text) {
                      this.textClearShow.value =
                          this.isHY ? text.isNotEmpty : false;
                    },
                    keyboardAppearance: Brightness.light,
                  );
                },
              ),
            ),
          ),
          _suffix(),
        ],
      ),
    );
  }

  Widget _suffix() {
    return ValueListenableBuilder<bool>(
      valueListenable: this.textClearShow,
      builder: (context, value, child) {
        return Visibility(
          visible: value,
          child: GestureDetector(
            onTap: () {
              _textController.clear();
              this.textClearShow.value = false;
            },
            child: SizedBox(
              width: 40,
              height: 34,
              child: Icon(
                Icons.cancel,
                size: 22,
                color: Color(0xFFcccccc),
              ),
            ),
          ),
        );
      },
    );
  }

  void searchAction(String value) {
    if (value.isEmpty) {
      showToast("请输入搜索内容");
      return;
    }
    if (this._controller.index == 0) {
      // 私海
      this.privateNotifier.value = value;
    } else {
      // 公海
      this.publicNotifier.value = value;
    }
  }

  @override
  String getTitleName() {
    return '';
  }
}
