import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/tools/tools.dart';
import 'package:XyyBeanSproutsFlutter/customer/data/customer_detail_data_v2.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/widget/ybm_customer_call_phone.dart';
import 'package:XyyBeanSproutsFlutter/utils/click_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class CustomerPublicDetailHeaderWidget extends StatelessWidget {
  final CustomerDetailDataV2? detailData;
  final VoidCallback? titleClickCallback;
  final VoidCallback? claimClickCallback;
  final VoidCallback? mapClickCallback;

  CustomerPublicDetailHeaderWidget(this.detailData,
      {this.claimClickCallback,
      this.titleClickCallback,
      this.mapClickCallback});

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8), color: Colors.white),
        padding: EdgeInsets.only(top: 15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            buildTitleWidget(context),
            SizedBox(
              height: 8,
            ),
            buildUnRegisterWidget(),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(child: buildDetailInfoWidget(context)),
                buildMapAndContactWidget(context)
              ],
            ),
            SizedBox(
              height: 11.5,
            ),
            buildClaimRecordWidget(),
          ],
        ),
      ),
    );
  }

  /// 地图和联系人
  Widget buildMapAndContactWidget(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: Row(
        children: [
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: mapClickCallback,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Image.asset(
                  "assets/images/customer/customer_private_detail_map.png",
                  width: 25,
                  height: 35,
                ),
                SizedBox(
                  height: 10,
                ),
                Text(
                  "地图",
                  style: TextStyle(
                      color: const Color(0xff676773),
                      fontSize: 11,
                      fontWeight: FontWeight.normal),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 蜂窝和客户id
  Widget buildCombWidget() {
    var style = TextStyle(
        color: const Color(0xff333333),
        fontSize: 12,
        fontWeight: FontWeight.normal);
    return Text(
      "所属蜂窝：${detailData?.combName?.toString() ?? "--"}",
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      style: style,
    );
  }

  /// 客户id
  Widget buildMerchantIdWidget() {
    var style = TextStyle(
        color: const Color(0xff333333),
        fontSize: 12,
        fontWeight: FontWeight.normal);
    return Text(
      "客户编码：${getMerchantIdFormat()}",
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      style: style,
    );
  }

  String getMerchantIdFormat() {
    var merchantId = detailData?.merchantId?.toString();
    if (merchantId == "-1" || merchantId == null) {
      return "--";
    }
    return merchantId;
  }

  /// 详细信息
  Widget buildDetailInfoWidget(BuildContext context) {
    return Container(
        padding: EdgeInsets.only(right: 10, left: 10),
        decoration: BoxDecoration(
            border: Border(
                right: BorderSide(
          color: const Color(0xffeeeeee),
          width: 0.5,
        ))),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            buildCombWidget(),
            SizedBox(height: 5,),
            buildMerchantIdWidget(),
            SizedBox(
              height: 5,
            ),
            Text(
              detailData?.address ?? "--",
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                  color: const Color(0xff333333),
                  fontSize: 12,
                  fontWeight: FontWeight.normal),
            ),
          ],
        ));
  }

  bool isRegister() {
    return detailData?.isRegister == true;
  }

  /// 未注册
  Widget buildUnRegisterWidget() {
    return Visibility(
      visible: !isRegister(),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 3, vertical: 2.5),
        margin: EdgeInsets.only(left: 10),
        decoration: BoxDecoration(
            color: const Color(0xfffff2f2),
            borderRadius: BorderRadius.circular(1)),
        child: Text(
          "未注册",
          style: TextStyle(
              color: const Color(0xffff2121),
              fontSize: 10,
              fontWeight: FontWeight.normal),
        ),
      ),
    );
  }

  /// 标题
  Widget buildTitleWidget(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: titleClickCallback,
      child: Container(
        alignment: Alignment.centerLeft,
        padding: const EdgeInsets.symmetric(horizontal: 10),
        child: RichText(
          textAlign: TextAlign.left,
          text: TextSpan(children: [
            WidgetSpan(
              alignment: PlaceholderAlignment.middle,
              child: Visibility(
                visible: detailData?.chainFlag == true,
                child: Container(
                  padding: EdgeInsets.only(right: 5),
                  child: Image.asset(
                    "assets/images/customer/customer_private_detail_chain.png",
                    width: 30,
                    height: 14,
                  ),
                ),
              ),
            ),
            WidgetSpan(
              alignment: PlaceholderAlignment.middle,
              child: Visibility(
                visible: detailData?.saasUserFlag == 1,
                child: Container(
                  padding: EdgeInsets.only(right: 5),
                  child: Image.asset(
                    "assets/images/customer/customer_saas.png",
                    width: 30,
                    height: 14,
                  ),
                ),
              ),
            ),
            TextSpan(
                text: detailData?.customerName?.toString() ?? "--",
                style: TextStyle(
                    fontSize: 16,
                    color: const Color(0xff333333),
                    fontWeight: FontWeight.w500)),
            WidgetSpan(
              alignment: PlaceholderAlignment.middle,
              child: Container(
                padding: const EdgeInsets.only(left: 5),
                child: Image.asset(
                  "assets/images/customer/customer_private_detail_copy.png",
                  width: 10,
                  height: 10,
                ),
              ),
            ),
          ]),
        ),
      ),
    );
  }

  /// 认领记录
  Widget buildClaimRecordWidget() {
    return GestureDetector(
      onTap: claimClickCallback,
      behavior: HitTestBehavior.opaque,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(12),
        alignment: Alignment.center,
        decoration: BoxDecoration(
            border: Border(
                top: BorderSide(color: const Color(0xfff5f5f5), width: 0.5))),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              "查看认领记录",
              style: TextStyle(
                  color: const Color(0xff00b377),
                  fontSize: 12,
                  fontWeight: FontWeight.w500),
            ),
            SizedBox(
              width: 5,
            ),
            Image.asset(
              "assets/images/customer/customer_public_detail_arrow.png",
              width: 12,
              height: 12,
            )
          ],
        ),
      ),
    );
  }
}
