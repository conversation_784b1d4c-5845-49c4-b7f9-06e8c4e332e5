import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/event_bus/event_bus.dart';
import 'package:XyyBeanSproutsFlutter/utils/global_random_utils.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/sub_page/all_goods/all_goods_page.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/sub_page/often_buy/page/customer_often_buy_page.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/sub_page/recommended_products/page/customer_recommended_products_page.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/sub_page/out_of_stock_items/page/customer_out_of_stock_items_page.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/sub_page/search_word/page/customer_search_word.dart';
import 'package:XyyBeanSproutsFlutter/goods/carts/shopping_carts_page.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/commodity_rank_page.dart';
import 'package:XyyBeanSproutsFlutter/goods/widgets/tab_custom_indicator.dart';
import 'package:flutter/material.dart';

class GoodsRecommendPage extends BasePage {
  final dynamic merchantId;
  final dynamic customerId;
  final dynamic selectedIndex;
  final dynamic saasUserFlag;

  GoodsRecommendPage({
    this.merchantId,
    this.customerId,
    this.selectedIndex = 0,
    this.saasUserFlag = '2',
  });

  @override
  BaseState<StatefulWidget> initState() {
    print('kkk,${this.saasUserFlag}');
    return GoodsRecommendPageState();
  }
}

class GoodsRecommendPageState extends BaseState<GoodsRecommendPage>
    with SingleTickerProviderStateMixin,EventBusObserver {
  // Tab标题数组 - 动态生成
  late List<String> titles;

  // 原始完整的标题数组（用于索引映射）
  final List<String> _allTitles = ["购物车","缺货品","推荐品", "常购品", "搜索品", "热销品", "全部商品"];

  // Tab控制器
  late TabController _controller;

  ValueNotifier<int> selectedIndexNotifier = ValueNotifier(0);

  static String id = '';
  static int intId = 0; ///0第一次进入，1缺货，2推荐，3热销

  @override
  void initState() {
    id = randomString6();
    intId = 0;
    this._generateTitles();
    this.initalTabController();
    super.initState();
  }
  @override
  void dispose() {
    _controller.dispose(); // 销毁控制器
    super.dispose();
  }
  // 根据 saasUserFlag 生成动态标题
  void _generateTitles() {
    if (widget.saasUserFlag != '1') {
      // 隐藏缺货品，移除索引1的项目
      titles = List.from(_allTitles);
      titles.removeAt(1); // 移除"缺货品"
      print('🔧 saasUserFlag!=1, 隐藏缺货品, titles: $titles');
    } else {
      // 显示所有标题
      titles = List.from(_allTitles);
      print('🔧 saasUserFlag=${widget.saasUserFlag}, 显示所有标题, titles: $titles');
    }
  }

  // 将原始索引映射到显示索引
  int _mapOriginalToDisplayIndex(int originalIndex) {
    if (widget.saasUserFlag != '1' && originalIndex > 1) {
      // 如果隐藏缺货品且原始索引大于1，则显示索引减1
      return originalIndex - 1;
    } else if (widget.saasUserFlag != '1' && originalIndex == 1) {
      // 如果要跳转到缺货品但被隐藏了，跳转到推荐品
      return 1; // 推荐品在隐藏缺货品后的索引
    }
    return originalIndex;
  }

  // 将显示索引映射回原始索引
  int _mapDisplayToOriginalIndex(int displayIndex) {
    if (widget.saasUserFlag != '1' && displayIndex >= 1) {
      // 如果隐藏缺货品且显示索引大于等于1，则原始索引加1
      return displayIndex + 1;
    }
    return displayIndex;
  }

  // 初始化Tab 控制器
  void initalTabController() {
    int originalIndex = int.tryParse("${widget.selectedIndex}") ?? 0;
    int displayIndex = _mapOriginalToDisplayIndex(originalIndex);

    print('🔧 索引映射: 原始索引=$originalIndex -> 显示索引=$displayIndex');

    this.selectedIndexNotifier.value = _mapDisplayToOriginalIndex(displayIndex);

    this._controller = TabController(
      length: this.titles.length,
      initialIndex: displayIndex,
      vsync: this,
    );
    this._controller.addListener(() {
      if (!this._controller.indexIsChanging) {
        this.selectedIndexNotifier.value = _mapDisplayToOriginalIndex(this._controller.index);
        print('切换tab成功');
       qt(this._controller.index);
      }
    });
  }
  void  qt(index){
    // 获取当前选中的Tab标题
    String currentTabTitle = titles[index];
    // 在这里添加你的Tab切换逻辑
    // 可以针对不同的Tab做不同处理
    switch(currentTabTitle) {
      case "推荐品":
      // 处理推荐品Tab切换逻辑
        intId = 1;
        eventBus.sendMessage(
          QT.qtYBMProductRecommendationPage + id,
        );
        break;
      case "缺货品":
      // 处理常购品Tab切换逻辑
        eventBus.sendMessage(
          QT.qtYBMOutStockItemsPage + id,
        );
        intId = 2;
        break;
    }
  }
  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      child: Column(
        children: [
          SizedBox(
            height: 44,
            child: TabBar(
              controller: this._controller,
              isScrollable: true,
              indicator: TabCustomIndicator(
                wantWidth: 28,
              ),
              indicatorSize: TabBarIndicatorSize.label,
              indicatorColor: Color(0xFF00B377),
              indicatorWeight: 3,
              unselectedLabelColor: Color(0xFF676773),
              unselectedLabelStyle: TextStyle(fontSize: 14),
              labelColor: Color(0xFF292933),
              labelStyle: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
              tabs: tabs(),
            ),
          ),
          // Divider(color: Color(0xFFF6F6F6), height: 0.5, thickness: 1),
          Expanded(
            child: TabBarView(
              controller: this._controller,
              physics: ClampingScrollPhysics(), // 启用左右滑动切换tab
              children: this.tabViews(),
            ),
          )
        ],
      ),
    );
  }

  // Tab Item
  List<Widget> tabs() {
    return this
        .titles
        .map((e) => Container(
              height: 28,
              child: Tab(
                text: e,
              ),
            ))
        .toList();
  }

  // TabView Item - 动态生成
  List<Widget> tabViews() {
    List<Widget> views = [];

    // 购物车 (索引0)
    views.add(ShoppingCartsPage(
      merchantId: widget.merchantId,
      customerId: widget.customerId,
    ));
    print('widget.saasUserFlag： ${widget.saasUserFlag}');
    // 缺货品 (索引1) - 根据 saasUserFlag 决定是否添加
    if (widget.saasUserFlag == '1') {
      views.add(CustomeroutOfStockItemsPage(
        merchantId: widget.merchantId,
        customerId: widget.customerId,
      ));
    }

    // 推荐品 (索引2)
    views.add(CustomerRecommendedProductsPage(
      merchantId: widget.merchantId,
      customerId: widget.customerId,
    ));

    // 常购品 (索引3)
    views.add(CustomerOftenBuyPage(
      merchantId: widget.merchantId,
      customerId: widget.customerId,
    ));

    // 搜索品 (索引4)
    views.add(CustomerSearchWordPage(merchantId: widget.merchantId));

    // 热销品 (索引5)
    views.add(CommodityRankPage(
      merchantId: widget.merchantId,
      customerId: widget.customerId,
      isSubPage: true,
    ));

    // 全部商品 (索引6)
    views.add(AllGoodsPage(customerId: widget.customerId));

    return views;
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(
      getTitleName(),
      rightButtons: [
        ValueListenableBuilder<int>(
          valueListenable: this.selectedIndexNotifier,
          builder: (context, value, child) {
            return Visibility(
              visible: value == 4,
              child: TextButton(
                onPressed: this.searchAction,
                child: Image.asset(
                  'assets/images/customer/customer_tab_search.png',
                  width: 22,
                  height: 22,
                ),
                style: ButtonStyle(
                  overlayColor:
                      MaterialStateProperty.all<Color>(Colors.transparent),
                  padding:
                      MaterialStateProperty.all<EdgeInsets>(EdgeInsets.all(10)),
                  minimumSize: MaterialStateProperty.all<Size>(Size.zero),
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
              ),
            );
          },
        )
      ],
    );
  }

  @override
  String getTitleName() {
    return "商品推荐";
  }

  void searchAction() {
    var router =
        "xyy://crm-app.ybm100.com/goods_search_history?customerId=${widget.customerId}&searchType=searchdetailGoodsManagerList&androidSearchType=7";
    router = Uri.encodeFull(router);
    XYYContainer.open(router);
  }
}

