import 'dart:convert';
import 'dart:io';
import 'dart:ui';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:qt_common_sdk/qt_common_sdk.dart';
import 'package:XyyBeanSproutsFlutter/utils/global_random_utils.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_data.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XYYContainer/tools/tools.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/dialog/check_license_dialog.dart';
import 'package:XyyBeanSproutsFlutter/common/dialog/common_alert_dialog.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/customer/common/popup/customer_detail_more_popover.dart';
import 'package:XyyBeanSproutsFlutter/customer/data/customer_sku_collect_data.dart';
import 'package:XyyBeanSproutsFlutter/customer/data/customer_detail_data_v2.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/data/customer_detail_purchase_trend_data.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/data/customer_detail_visit_data.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/widget/customer_private_detail_data_widget.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/widget/customer_private_detail_header_widget.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/widget/customer_private_detail_menu_widget.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/widget/customer_private_detail_purchase_trend_widget.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/widget/ybm_customer_call_phone.dart';
import 'package:XyyBeanSproutsFlutter/customer/public/widget/customer_sku_collect_dialog.dart';
import 'package:XyyBeanSproutsFlutter/customer/widget/customer_detail_visit_record_widget.dart';
import 'package:XyyBeanSproutsFlutter/main/data/user_auth_model.dart';
import 'package:XyyBeanSproutsFlutter/schedule/data/schedule_external_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/click_util.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class CustomerPrivateDetailPageV2 extends BasePage {
  final String? customerId; // 原merchantId
  final String? distributable;
  final String? licenseValidateMust;
  final String? licenseValidateIssue;
  final String? merchantStatusName;

  CustomerPrivateDetailPageV2(
      {@required this.customerId,
      @required this.distributable,
      @required this.licenseValidateIssue,
      @required this.licenseValidateMust,
      @required this.merchantStatusName});

  @override
  BaseState<StatefulWidget> initState() {
    return CustomerPrivateDetailPageState();
  }
}

class CustomerPrivateDetailPageState
    extends BaseState<CustomerPrivateDetailPageV2> {
  String? id = Platform.isAndroid ? '4_1' : '4_2';
  String pdcHost = "";

  UserInfoData? userInfo;

  String spme = '';
  /// 刷新控制器
  EasyRefreshController _controller = EasyRefreshController();

  /// 用来保存NavigationBar右侧按钮
  GlobalKey _anchorKey = GlobalKey();

  ValueNotifier<CustomerDetailDataV2?> detailDataNotifier = ValueNotifier(null);
  ValueNotifier<CustomerDetailPurchaseTrendData?> purchaseTrendDataNotifier =
      ValueNotifier(null);
  ValueNotifier<CustomerDetailVisitData?> visitDataNotifier =
      ValueNotifier(null);

  CustomerDetailDataV2? get detailData => detailDataNotifier.value;

  CustomerDetailVisitData? get visitData => visitDataNotifier.value;

  ScrollController scrollController = ScrollController();
  ValueNotifier<double> headerBgOffsetNotifier = ValueNotifier(0);

  @override
  void onCreate() {
    super.onCreate();
    scrollController.addListener(scrollCallback);
    XYYContainer.bridgeCall('app_host').then((value) {
      if (value is Map) {
        setState(() {
          pdcHost = value['h5_host'];
        });
      }
    });
    requestAllData();
  }

  void scrollCallback() {
    headerBgOffsetNotifier.value = scrollController.offset;
  }

  @override
  void dispose() {
    super.dispose();
    this._controller.dispose();
    scrollController.removeListener(scrollCallback);
    detailDataNotifier.dispose();
    purchaseTrendDataNotifier.dispose();
    visitDataNotifier.dispose();
    this.getUserInfo();
  }

  /// 通过桥方法 获取用户信息
  void getUserInfo() async{
    userInfo = await UserInfoUtil.getUserInfo();
    print('userInfo,${userInfo}');
  }

  @override
  Widget buildWidget(BuildContext context) {
    var paddingTop = MediaQuery.of(context).padding.top + 44;
    if (paddingTop <= 44) {
      // 兜底策略，如果状态栏高度不能正常获取，则手动写个高度，防止展示不出来
      paddingTop = 100;
    }
    return Container(
        width: getScreenWidth(),
        height: getScreenHeight(),
        color: Color(0xfff1f6f9),
        child: Stack(
          children: [
            Container(
              padding: EdgeInsets.only(top: paddingTop),
              child: Column(
                children: [
                  Expanded(
                    child: Container(
                      child: Stack(
                        children: [
                          Positioned(
                            top: 0,
                            child: ValueListenableBuilder<double>(
                                valueListenable: headerBgOffsetNotifier,
                                builder: (context, value, child) {
                                  if (value < 0) {
                                    value = 0;
                                  }
                                  return Transform.translate(
                                    offset: Offset(0, -paddingTop - value),
                                    child: Image.asset(
                                      "assets/images/customer/customer_private_detail_header.png",
                                      fit: BoxFit.fitWidth,
                                      width: getScreenWidth(),
                                    ),
                                  );
                                }),
                          ),
                          EasyRefresh(
                            controller: _controller,
                            onRefresh: () async {
                              return await requestAllData();
                            },
                            onLoad: null,
                            emptyWidget: null,
                            child: SingleChildScrollView(
                              controller: scrollController,
                              child: Container(
                                padding: EdgeInsets.symmetric(horizontal: 10),
                                width: getScreenWidth(),
                                child: Column(
                                  children: [
                                    SizedBox(
                                      height: 5,
                                    ),
                                    ValueListenableBuilder<
                                            CustomerDetailDataV2?>(
                                        valueListenable: detailDataNotifier,
                                        builder: (context, value, child) {
                                          return CustomerPrivateDetailHeaderWidget(
                                            value,
                                            moreClickCallback:
                                                jumpBasicInfoPage,
                                            titleClickCallback:
                                                showCopyActionSheet,
                                            phoneClickCallback: callPhoneOnTap,
                                            mapClickCallback: jumpAddressPage,
                                            tipsClickCallback: showLevelTips,
                                          );
                                        }),
                                    SizedBox(
                                      height: 10,
                                    ),
                                    ValueListenableBuilder<
                                            CustomerDetailDataV2?>(
                                        valueListenable: detailDataNotifier,
                                        builder: (context, value, child) {
                                          return Visibility(
                                              visible: isRegister(),
                                              child: Column(
                                                children: [
                                                  ValueListenableBuilder<
                                                          CustomerDetailPurchaseTrendData?>(
                                                      valueListenable:
                                                          purchaseTrendDataNotifier,
                                                      builder: (context, value,
                                                          child) {
                                                        return CustomerPrivateDetailDataWidget(
                                                          orderCount:
                                                              value?.orderCount,
                                                          refundOrderCount: value
                                                              ?.refundOrderCount,
                                                          couponCount: value
                                                              ?.couponCount,
                                                          orderClickCallback:
                                                              jumpOrderPage,
                                                          refundOrderClickCallback:
                                                              jumpRefundOrderPage,
                                                          couponClickCallback:
                                                              jumpCouponPage,
                                                        );
                                                      }),
                                                  SizedBox(
                                                    height: 10,
                                                  ),
                                                  CustomerPrivateDetailMenuWidget(
                                                    recommendedProductsCallback:
                                                      jumpRecommendedProductsPage,
                                                    outOfStockItemsCallback:
                                                    jumPoutOfStockItemsPage,
                                                    detailData:detailData,
                                                    frequentGoodsClickCallback:
                                                        jumpFrequentGoodsPage,
                                                    hotSellClickCallback:
                                                        jumpHotSellPage,
                                                    searchRecordClickCallback:
                                                        jumpSearchRecordPage,
                                                  ),
                                                  SizedBox(
                                                    height: 10,
                                                  ),
                                                  CustomerPrivateDetailPurchaseTrendWidget(
                                                    purchaseTrendNotifier:
                                                        purchaseTrendDataNotifier,
                                                    moreClickCallback:
                                                        jumpPurchaseTrendPage,
                                                  ),
                                                  SizedBox(
                                                    height: 10,
                                                  ),
                                                ],
                                              ));
                                        }),
                                    ValueListenableBuilder<
                                            CustomerDetailVisitData?>(
                                        valueListenable: visitDataNotifier,
                                        builder: (context, value, child) {
                                          return CustomerDetailVisitRecordWidget(
                                              visitData, jumpVisitPage);
                                        }),
                                    SizedBox(
                                      height: 10,
                                    )
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  ValueListenableBuilder<CustomerDetailDataV2?>(
                      valueListenable: detailDataNotifier,
                      builder: (context, value, child) {
                        return Visibility(
                          visible: detailData?.customerName != null,
                          child: Container(
                            color: Colors.white,
                            width: double.infinity,
                            padding: EdgeInsets.only(
                                left: 10, right: 10, top: 10, bottom: 20),
                            child: GestureDetector(
                              behavior: HitTestBehavior.opaque,
                              onTap: showAddVisitActionAlert,
                              child: Container(
                                width: double.infinity,
                                padding: EdgeInsets.symmetric(vertical: 11),
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                    color: const Color(0xff00b377),
                                    borderRadius: BorderRadius.circular(2)),
                                child: Text(
                                  "添加拜访",
                                  style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500),
                                ),
                              ),
                            ),
                          ),
                        );
                      }),
                ],
              ),
            ),
            Image.asset(
              "assets/images/customer/customer_private_detail_header.png",
              fit: BoxFit.fitWidth,
              height: paddingTop,
              alignment: Alignment.topCenter,
              width: getScreenWidth(),
            ),
          ],
        ));
  }

  /// 请求所有数据
  Future<void> requestAllData() async {
    showLoadingDialog();
    await Future.wait(
        [requestDetailData(), requestVisitData(), requestPurchasedTrendData()]);
    dismissLoadingDialog();
  }

  /// 请求客户详情数据
  Future<void> requestDetailData() async {
    var result = await NetworkV2<CustomerDetailDataV2>(CustomerDetailDataV2())
        .requestDataV2('customerV2/privateDetail',
            parameters: {'customerId': widget.customerId},
            method: RequestMethod.GET);
    if (mounted && result.isSuccess == true) {
      detailDataNotifier.value = result.getData();
      if(this.detailDataNotifier?.value?.id > 0){
        this.highSeasCustomerPageExposure(this.detailDataNotifier?.value?.id);
      }
    }
  }
  ///埋点门店类型子模块点击
  void highSeasCustomerPageExposure(value) async{
    var userInfo = await UserInfoUtil.getUserInfo();
    String spm = '';
    print('userInfo:${userInfo?.sessionId},${userInfo?.phone}');
    String sessionId = userInfo?.sessionId ?? '';
    spme = randomString6();
    spm =  sessionId + spme;
    /// 手动埋点：私海客户曝光页面曝光
    QTCommonSdk.onEvent("page_exposure", {
      'spm_cnt': '$id.customerDetailPrivacy_' + value.toString() + '-0_0' + '.0.0.' +  spm,
    });
    print('私海客户页面曝光');
    ///组件曝光
    QTCommonSdk.onEvent("page_component_exposure", {
      'spm_cnt': '$id.customerDetailPrivacy_' + value.toString() + '-0_0.prodTrade@4.0.' +  spm,
    });
    print('私海客户商品推荐组件曝光');
  }

  /// 请求拜访记录数据
  Future<void> requestVisitData() async {
    var result =
        await NetworkV2<CustomerDetailVisitData>(CustomerDetailVisitData())
            .requestDataV2('customerV2/visitList',
                parameters: {'customerId': widget.customerId},
                method: RequestMethod.GET);
    if (mounted && result.isSuccess == true) {
      visitDataNotifier.value = result.getData();
    }
  }

  /// 请求采购趋势数据
  Future<void> requestPurchasedTrendData() async {
    var result = await NetworkV2<CustomerDetailPurchaseTrendData>(
            CustomerDetailPurchaseTrendData())
        .requestDataV2('customerV2/purchaseTrend',
            parameters: {'customerId': widget.customerId},
            method: RequestMethod.GET);
    if (mounted && result.isSuccess == true) {
      purchaseTrendDataNotifier.value = result.getData();
    }
  }

  @override
  bool isExtendBodyBehindAppBar() {
    return true;
  }

  @override
  String getTitleName() {
    return "客户详情（B2B）";
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(
      getTitleName(),
      backgroundColor: Colors.transparent,
      leftButtonColor: Colors.white,
      titleTextColor: Colors.white,
      rightButtons: [
        IconButton(
          key: this._anchorKey,
          padding: EdgeInsets.zero,
          highlightColor: Colors.transparent,
          hoverColor: Colors.transparent,
          color: Colors.white,
          splashColor: Colors.transparent,
          icon: ImageIcon(
            AssetImage(
              'assets/images/customer/customer_detail_more.png',
            ),
            color: Colors.white,
            size: 24,
          ),
          onPressed: () {
            showCustomerMoreMenu(
              context: this.context,
              targetKey: this._anchorKey,
              itemSource: getCustomerMoreList(),
              itemTap: this.jumpMoreItem,
            );
          },
        )
      ],
    );
  }

  List<CustomerMoreSourceItem> getCustomerMoreList() {
    var sourceList = <CustomerMoreSourceItem>[];
    if (detailData?.allocationFlag?.toString() == "true") {
      sourceList
          .add(CustomerMoreSourceItem(title: '分配至BD', id: "distributeToBD"));
    }
    sourceList.addAll([
      CustomerMoreSourceItem(title: '释放至公海', id: "releaseToPublic"),
      CustomerMoreSourceItem(title: '客户错误信息上报', id: "uploadError")
    ]);
    return sourceList;
  }

  void jumpMoreItem(dynamic id) {
    if (detailData?.customerName == null) {
      return;
    }
    debounce(() {
      switch (id) {
        case "distributeToBD":
          distributeToBD();
          break;
        case "releaseToPublic":
          releaseToPublic();
          break;
        case "uploadError":
          uploadError();
          break;
      }
    });
  }

  /// 分配至BD
  void distributeToBD() async {
    var sysUserId = await UserInfoUtil.sysUserId();
    // 重置商品集选中信息
    detailData?.bindSkuCollect?.forEach((element) {
      element.isCheck = false;
    });
    List<CustomerSkuCollectData> list = detailData?.bindSkuCollect
            ?.where((element) => element.oaUserId?.toString() == sysUserId)
            .toList() ??
        [];
    track("Event-PrivateSeaDetail-Distribute");
    if (list.length > 1) {
      List<CustomerSkuCollectData>? result =
          await CustomerSkuCollectDialog.showSkuCollectDialog(context, list,
              title: "选择要分配的商品集",
              rightText: "下一步",
              canSelectIgnoreReceiveType: true);
      if (result != null && result.length > 0) {
        this.jumpToSelectPeople(result);
      }
    } else if (list.length == 1) {
      this.jumpToSelectPeople(list);
    } else {
      XYYContainer.toastChannel.toast("暂无可分配商品集");
    }
  }

  /// 跳转选择分配人员
  void jumpToSelectPeople(List<CustomerSkuCollectData> result) {
    String routerPath =
        "xyy://crm-app.ybm100.com/alloc_people?type=1&isAccompanySelect=1";
    XYYContainer.open(routerPath, callback: (value) {
      if (value != null) {
        String? peopleId = value["selectdId"];
        String skuCollectCodes = result.map((e) => e.skuCollectCode).join(',');
        if (peopleId != null && skuCollectCodes.length > 0) {
          this.requestAlloc(peopleId, skuCollectCodes);
        }
      }
    });
  }

  /// 请求分配
  void requestAlloc(String bindUserId, String skuCollectCodes) async {
    showLoadingDialog();
    var result =
        await NetworkV2<NetworkBaseModelV2>(NetworkBaseModelV2()).requestDataV2(
      'customer/private/bind/user',
      parameters: {
        'bindUserId': bindUserId,
        'skuCollectCodes': skuCollectCodes,
        'customerId': widget.customerId,
      },
      method: RequestMethod.POST,
      contentType: RequestContentType.FORM,
    );
    dismissLoadingDialog();
    if (result.isSuccess == true) {
      track("Event-PrivateSeaDetail-Distribute-Success");
      XYYContainer.toastChannel.toast("分配成功");
      // 分配成功后刷新页面数据
      showLoadingDialog();
      requestDetailData();
    }
  }

  /// 释放至公海
  void releaseToPublic() async {
    if (widget.customerId == null) {
      showToast("customerId字段错误");
      return;
    }
    if (detailData?.unbindFlag != true) {
      showToast("无权限释放该客户");
      return;
    }
    track("Event-PrivateSeaDetail-Release");
    var sysUserId = await UserInfoUtil.sysUserId();
    // 重置商品集选中信息
    detailData?.bindSkuCollect?.forEach((element) {
      element.isCheck = false;
    });
    List<CustomerSkuCollectData> list = detailData?.bindSkuCollect
            ?.where((element) => element.oaUserId?.toString() == sysUserId)
            .toList() ??
        [];
    if (list.length > 1) {
      List<CustomerSkuCollectData>? result =
          await CustomerSkuCollectDialog.showSkuCollectDialog(context, list,
              title: "选择要释放商品集",
              rightText: "释放",
              canSelectIgnoreReceiveType: true);
      if (result != null && result.length > 0) {
        this.releaseToPublicReal(result);
      }
    } else if (list.length == 1) {
      this.releaseToPublicReal(list);
    } else {
      XYYContainer.toastChannel.toast("暂无可释放商品集");
    }
  }

  /// 释放至公海（调用接口）
  void releaseToPublicReal(List<CustomerSkuCollectData> skuCollectList) async {
    showMessageDialog2(
        title: "",
        message: "是否释放该商品集到公海",
        callBack: () async {
          showLoadingDialog();
          var result = await NetworkV2<NetworkBaseModelV2>(NetworkBaseModelV2())
              .requestDataV2('customer/private/unBindCustomer',
                  parameters: {
                    'skuCollectCodes':
                        skuCollectList.map((e) => e.skuCollectCode).join(','),
                    'id': widget.customerId,
                  },
                  method: RequestMethod.GET);
          dismissLoadingDialog();
          if (result.isSuccess == true) {
            track("Event-PrivateSeaDetail-Release-Success");
            XYYContainer.toastChannel.toast("释放到公海成功");
            XYYContainer.close(context, resultData: {"release": "1"});
          }
        });
  }

  /// 错误信息上报
  void uploadError() {
    track("Event-PrivateSeaDetail-UploadError");
    if (this.detailData?.poiAuditStatus != null) {
      if (this.detailData?.poiAuditStatus.toString() == "1") {
        showToast("客户信息审核中，无法提交审核");
        return;
      }
      if (this.detailData?.poiAuditStatus.toString() == "3") {
        showToast("客户信息审核驳回，无法操作");
        return;
      }
    }
    track("Event-PrivateSeaDetail-UploadError-Success");
    String pdcUrl =
        this.pdcHost + "/dataError?poiId=${this.detailData?.poiId}&source=5";
    pdcUrl = Uri.encodeComponent(pdcUrl);
    String router = "xyy://crm-app.ybm100.com/crm/web_view?url=$pdcUrl";
    XYYContainer.open(router);
  }

  /// 跳转地图页
  void jumpAddressPage() {
    if (this.detailData?.id == null) {
      return;
    }
    // String router = "xyy://crm-app.ybm100.com/customer/customer_map?";
    // router = router + "id=${this.detailData?.id}&";
    // router = router + "poiLatitude=${this.detailData?.poiLatitude}&";
    // router = router + "poiLongitude=${this.detailData?.poiLongitude}&";
    // router = router + "address=${this.detailData?.address}&";
    // router = router + "customerName=${this.detailData?.customerName}&";
    // router = router + "poiId=${this.detailData?.poiId}&";
    // router = router + "type=false&";
    // router = router + "changePoi=true&";
    // router = router + "changeMap=true";
    // router = Uri.encodeFull(router);
    // XYYContainer.open(router);

    String router = "/ybm_customer_map_page?";
    router = router + "customerId=${this.detailData?.id}&";
    router = router + "latitude=${this.detailData?.poiLatitude}&";
    router = router + "longitude=${this.detailData?.poiLongitude}";
    router = Uri.encodeFull(router);
    XYYContainer.open(router);
  }

  bool isRegister() {
    return detailData?.isRegister == true;
  }

  /// 打电话
  void callPhoneOnTap() {
    track("mc-customer-telphone");
    if (detailData?.id == null || detailData?.customerName == null) {
      return;
    }

    if (detailData?.contactList?.isNotEmpty ?? true) {
      YBMCustomerCallPhoneWidget.showContactListView(
          context: context,
          contactList: detailData?.contactList ?? [],
          customerId: "${detailData?.id ?? ""}",
          customerName: detailData?.customerName ?? "--");
      return;
    }

    if (!isRegister()) {
      // 未注册，直接拨打poi电话
      if (detailData?.poiMobilePhone == null ||
          (detailData?.poiMobilePhone?.toString().isEmpty ?? true)) {
        XYYContainer.toastChannel.toast("暂无电话");
        return;
      }
      debounce(() {
        XYYContainer.bridgeCall('call_phone', parameters: {
          'mobile': '${detailData?.poiMobilePhone}',
          'merchantId': '${detailData?.id}',
          'merchantName': '${detailData?.customerName}',
          'addSource': enumToString(CallAddSource.privateDetail),
        });
      });
    } else {
      // 已注册，展示联系人列表
      // if (detailData?.contactList?.isEmpty ?? true) {
      XYYContainer.toastChannel.toast("联系人为空，请添加联系人");
      // return;
      // }
      // YBMCustomerCallPhoneWidget.showContactListView(
      //     context: context,
      //     contactList: detailData?.contactList ?? [],
      //     customerId: "${detailData?.id ?? ""}",
      //     customerName: detailData?.customerName ?? "--");
    }
  }

  /// 展示复制弹窗
  void showCopyActionSheet() {
    showCupertinoModalPopup<void>(
      context: context,
      builder: (BuildContext context) {
        return CupertinoActionSheet(
          message: Text(
              '${this.detailData?.customerName}（客户编号:${this.detailData?.merchantId}）'),
          actions: [
            CupertinoActionSheetAction(
              onPressed: () {
                Clipboard.setData(
                  ClipboardData(
                      text:
                          '${this.detailData?.customerName}（客户编号:${this.detailData?.merchantId}）'),
                );
                Navigator.of(context).pop();
                XYYContainer.toastChannel.toast('复制成功');
              },
              child: Text('复制'),
            ),
          ],
          cancelButton: CupertinoActionSheetAction(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: Text('取消'),
          ),
        );
      },
    );
  }

  /// 展示等级信息
  void showLevelTips() {
    if (detailData?.crmCustomerLevel?.tips == null) {
      return;
    }
    showCommonAlert(
      context: this.context,
      title: "提示",
      contentTextAlign: TextAlign.left,
      content: detailData?.crmCustomerLevel?.tips ?? "--",
      actions: [
        CommonAlertAction(
          title: "好的",
        ),
      ],
    );
  }

  void showAddVisitActionAlert() {
    if(widget.licenseValidateMust == null || widget.licenseValidateIssue == null || widget.merchantStatusName == null){
      this.addVisit();
    }

    var licenseValidateMust = int.parse(widget.licenseValidateMust!);
    var licenseValidateIssue = int.parse(widget.licenseValidateIssue!);
    CheckLicenseDialog().showCheckLicenseDialog(
        context,
        licenseValidateMust,
        licenseValidateIssue,
        widget.merchantStatusName == "已冻结",
        continueVisitCallback: addVisit);
  }

  /// 添加拜访
  void addVisit() async {
    EasyLoading.show(maskType: EasyLoadingMaskType.clear);
    var result = await NetworkV2<ScheduleExternalModel>(ScheduleExternalModel())
        .requestDataV2(
      'task/v290/toAddVisit',
      contentType: RequestContentType.FORM,
      method: RequestMethod.GET,
      parameters: {
        'customerId': widget.customerId,
        'customerType': 1,
      },
    );
    EasyLoading.dismiss();
    if (result.isSuccess == true) {
      bool isBDM = await UserInfoUtil.isBDMOrGJRBDM();
      String roleJSON = await UserAuthManager.getRoleJSONString();
      String externalJson = jsonEncode(result.getData()?.toJson() ?? {});
      // BDM、跟进人BDM跳转陪访   BD、跟进人跳添加拜访
      String roleStr = Uri.encodeComponent(roleJSON);
      String externalStr = Uri.encodeComponent(externalJson);
      if (isBDM) {
        var router =
            '/add_accompany_visit_page?rolesJSON=$roleStr&externalJson=$externalStr';
        XYYContainer.open(router);
      } else {
        var router =
            '/add_visit_page?rolesJSON=$roleStr&externalJson=$externalStr';
        XYYContainer.open(router);
      }
    }
  }

  /// 跳转订单记录
  void jumpOrderPage() {
    track("mc-customer-cusorder");
    XYYContainer.open("xyy://crm-app.ybm100.com/drugstore/detail/item?"
        "merchantId=${widget.customerId}"
        "&position=1"
        "&merchantName=${Uri.encodeFull(detailData?.customerName ?? "")}"
        "&registerFlag=${detailData?.registerFlag}");
  }

  /// 跳转基本信息
  void jumpBasicInfoPage() {
    track("mc-customer-data");
    if (isRegister()) {
      XYYContainer.open("xyy://crm-app.ybm100.com/drugstore/detail/item?"
          "merchantId=${widget.customerId}"
          "&position=0"
          "&merchantName=${Uri.encodeFull(detailData?.customerName ?? "")}"
          "&registerFlag=${detailData?.registerFlag}");
    } else {
      XYYContainer.open(
          "/customer_basic_info_page?customerId=${detailData?.id}&registerFlag=${detailData?.registerFlag}");
    }
  }

  /// 跳转退单记录
  void jumpRefundOrderPage() {
    track("mc-customer-chargeback");
    XYYContainer.open(
        "/customer_refund_order_page?merchantId=${detailData?.merchantId}");
  }

  /// 跳转优惠券
  void jumpCouponPage() {
    track("mc-customer-coupon");
    XYYContainer.open(
        "/customer_coupons_page?merchantId=${this.detailData?.merchantId}");
  }

  /// 跳转搜索记录
  void jumpSearchRecordPage() {
    track("mc-customer-search");
    String router =
        "/goods_recommend_page?merchantId=${detailData?.merchantId}&customerId=${widget.customerId}&selectedIndex=4";
    router = Uri.encodeFull(router);
    XYYContainer.open(router);
  }

  /// 跳转热销榜单
  void jumpHotSellPage() {
    track("mc-customer-hotgoods");
    clickQT('3','热销榜单');
    String router =
        "/goods_recommend_page?merchantId=${detailData?.merchantId}&customerId=${widget.customerId}&selectedIndex=5&saasUserFlag=${detailData?.saasUserFlag}";
    router = Uri.encodeFull(router);
    XYYContainer.open(router);
  }

  /// 跳转推荐商品
  void jumpRecommendedProductsPage() {
    track("mc-customer-offgoods");
    clickQT('1','推荐商品');
    String router =
        "/goods_recommend_page?merchantId=${detailData?.merchantId}&customerId=${widget.customerId}&selectedIndex=2&saasUserFlag=${detailData?.saasUserFlag}";
    router = Uri.encodeFull(router);
    XYYContainer.open(router);
  }
  ///子模块点击
  void clickQT(code,text) async{
    var userInfo = await UserInfoUtil.getUserInfo();
    String spm = '';
    print('userInfo:${userInfo?.sessionId},${userInfo?.phone}');
    String sessionId = userInfo?.sessionId ?? '';
    spm =  sessionId + spme;
    String spmId14 = randomString(14);
    // 手动埋点
    QTCommonSdk.onEvent("action_sub_module_click", {
      'spm_cnt': '$id.customerDetailPrivacy_' + (this.detailDataNotifier?.value?.id).toString() + '-0_0' + '.<EMAIL>@' + code + '.' +  spm,
      'scm_cnt':'appFE.0.all_0.text-' + text + '.' + spmId14,
    });
    print('私海客户详情点击商品推荐模块-${text}');
  }
  /// 跳转缺货品
  void jumPoutOfStockItemsPage() {
    track("mc-customer-offgoods");
    clickQT('2','缺货商品');
    String router =
        "/goods_recommend_page?merchantId=${detailData?.merchantId}&customerId=${widget.customerId}&selectedIndex=1&saasUserFlag=${detailData?.saasUserFlag}";
    router = Uri.encodeFull(router);
    XYYContainer.open(router);

  }

  /// 跳转常购商品
  void jumpFrequentGoodsPage() {
    track("mc-customer-offgoods");
    String router =
        "/goods_recommend_page?merchantId=${detailData?.merchantId}&customerId=${widget.customerId}&selectedIndex=3";
    router = Uri.encodeFull(router);
    XYYContainer.open(router);
  }

  /// 跳转拜访记录
  void jumpVisitPage() {
    track("mc-customer-cusvisit");
    XYYContainer.open(
        "/customer_schedule_record_page?type=0&customerId=${widget.customerId}");
  }

  /// 跳转销售趋势
  void jumpPurchaseTrendPage() {
    // do nothing
  }
}
