import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/tools/tools.dart';
import 'package:XyyBeanSproutsFlutter/customer/data/customer_detail_data_v2.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/widget/ybm_customer_call_phone.dart';
import 'package:XyyBeanSproutsFlutter/utils/click_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class CustomerPrivateDetailHeaderWidget extends StatelessWidget {
  final CustomerDetailDataV2? detailData;
  final VoidCallback? moreClickCallback;
  final VoidCallback? titleClickCallback;
  final VoidCallback? phoneClickCallback;
  final VoidCallback? mapClickCallback;
  final VoidCallback? tipsClickCallback;

  CustomerPrivateDetailHeaderWidget(this.detailData,
      {this.moreClickCallback,
      this.phoneClickCallback,
      this.titleClickCallback,
      this.mapClickCallback,
      this.tipsClickCallback});

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8), color: Colors.white),
        padding: EdgeInsets.only(top: 15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            buildTitleWidget(context),
            SizedBox(
              height: 8,
            ),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(child: buildDetailInfoWidget(context)),
                buildMapAndContactWidget(context)
              ],
            ),
            SizedBox(
              height: 17.5,
            ),
            buildPurchaseAndVisitWidget(),
          ],
        ),
      ),
    );
  }

  /// 地图和联系人
  Widget buildMapAndContactWidget(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(right: 5, left: 10),
      child: Row(
        children: [
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: mapClickCallback,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Image.asset(
                  "assets/images/customer/customer_private_detail_map.png",
                  width: 25,
                  height: 35,
                ),
                SizedBox(
                  height: 10,
                ),
                Text(
                  "地图",
                  style: TextStyle(
                      color: const Color(0xff676773),
                      fontSize: 11,
                      fontWeight: FontWeight.normal),
                )
              ],
            ),
          ),
          SizedBox(
            width: 10,
          ),
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: phoneClickCallback,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Image.asset(
                  "assets/images/customer/customer_private_detail_contact.png",
                  width: 25,
                  height: 35,
                ),
                SizedBox(
                  height: 10,
                ),
                Text(
                  "联系人",
                  style: TextStyle(
                      color: const Color(0xff676773),
                      fontSize: 11,
                      fontWeight: FontWeight.normal),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 客户等级
  Widget buildCustomerLevelWidget(BuildContext context) {
    return Visibility(
      visible: isRegister(),
      child: Container(
        padding: EdgeInsets.only(top: 10),
        child: Row(
          children: [
            Image.asset(
              "assets/images/customer/customer_private_detail_level.png",
              width: 14,
              height: 11.5,
            ),
            SizedBox(
              width: 5,
            ),
            Text(
              "客户等级：${detailData?.crmCustomerLevel?.lastMonthLevelName ?? "--"}",
              style: TextStyle(
                  color: const Color(0xff333333),
                  fontSize: 11,
                  fontWeight: FontWeight.w500),
            ),
            SizedBox(width: 5,),
            Text(
              '(当前评级：${detailData?.crmCustomerLevel?.currentLevelName ?? "--"})',
              style: TextStyle(
                  fontSize: 11,
                  color: const Color(0xff9494a5),
                  fontWeight: FontWeight.normal),
            ),
            SizedBox(
              width: 5,
            ),
            GestureDetector(
              onTap: tipsClickCallback,
              child: Image.asset(
                "assets/images/customer/customer_private_detail_level_tip.png",
                width: 12,
                height: 12,
              ),
            )
          ],
        ),
      ),
    );
  }

  /// 蜂窝和客户id
  Widget buildCombWidget() {
    var style = TextStyle(
        color: const Color(0xff333333),
        fontSize: 12,
        fontWeight: FontWeight.normal);
    return Text(
      "所属蜂窝：${detailData?.combName?.toString() ?? "--"}",
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      style: style,
    );
  }

  /// 客户id
  Widget buildMerchantIdWidget() {
    var style = TextStyle(
        color: const Color(0xff333333),
        fontSize: 12,
        fontWeight: FontWeight.normal);
    return Text(
      "客户编码：${getMerchantIdFormat()}",
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      style: style,
    );
  }

  String getMerchantIdFormat() {
    var merchantId = detailData?.merchantId?.toString();
    if (merchantId == "-1" || merchantId == null) {
      return "--";
    }
    return merchantId;
  }

  /// 详细信息
  Widget buildDetailInfoWidget(BuildContext context) {
    return Container(
        padding: EdgeInsets.only(right: 10, left: 10),
        decoration: BoxDecoration(
            border: Border(
                right: BorderSide(
          color: const Color(0xffeeeeee),
          width: 0.5,
        ))),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            buildLicenseAndInvoiceWidget(context),
            SizedBox(
              height: 10,
            ),
            buildCombWidget(),
            SizedBox(height: 5,),
            buildMerchantIdWidget(),
            SizedBox(
              height: 5,
            ),
            Text(
              detailData?.address ?? "--",
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                  color: const Color(0xff333333),
                  fontSize: 12,
                  fontWeight: FontWeight.normal),
            ),
            buildCustomerLevelWidget(context)
          ],
        ));
  }

  bool isRegister() {
    return detailData?.isRegister == true;
  }

  /// 资质、发票、更多资料
  Widget buildLicenseAndInvoiceWidget(BuildContext context) {
    return Row(
      children: [
        Visibility(
          visible: !isRegister(),
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 3, vertical: 2.5),
            decoration: BoxDecoration(
                color: const Color(0xfffff2f2),
                borderRadius: BorderRadius.circular(1)),
            child: Text(
              "未注册",
              style: TextStyle(
                  color: const Color(0xffff2121),
                  fontSize: 10,
                  fontWeight: FontWeight.normal),
            ),
          ),
        ),
        Visibility(
          visible: isRegister(),
          child: Row(
            children: [
              Visibility(
                visible: detailData?.licenseStatusName?.toString().isNotEmpty ==
                    true,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 3, vertical: 2.5),
                  decoration: BoxDecoration(
                      color: const Color(0xfffff2e8),
                      borderRadius: BorderRadius.circular(1)),
                  child: Text(
                    detailData?.licenseStatusName?.toString() ?? "--",
                    style: TextStyle(
                        color: const Color(0xffff7200),
                        fontSize: 10,
                        fontWeight: FontWeight.normal),
                  ),
                ),
              ),
              Visibility(
                visible:
                    detailData?.invoiceTypeName?.toString().isNotEmpty == true,
                child: Container(
                  margin: EdgeInsets.only(left: 10),
                  padding: EdgeInsets.symmetric(horizontal: 3, vertical: 2.5),
                  decoration: BoxDecoration(
                      color: const Color(0xffe9fff8),
                      borderRadius: BorderRadius.circular(1)),
                  child: Text(
                    detailData?.invoiceTypeName?.toString() ?? "--",
                    style: TextStyle(
                        color: const Color(0xff00b377),
                        fontSize: 10,
                        fontWeight: FontWeight.normal),
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(width: 10),
        GestureDetector(
          onTap: moreClickCallback,
          behavior: HitTestBehavior.opaque,
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2.5),
            decoration: BoxDecoration(
                color: const Color(0xff00b377),
                borderRadius: BorderRadius.circular(10)),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  "更多资料",
                  style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.normal),
                ),
                SizedBox(
                  width: 3.5,
                ),
                Image.asset(
                  "assets/images/customer/customer_private_detail_white_arrow.png",
                  width: 4,
                  height: 6,
                )
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 标题
  Widget buildTitleWidget(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: titleClickCallback,
      child: Container(
        alignment: Alignment.centerLeft,
        padding: const EdgeInsets.symmetric(horizontal: 10),
        child: RichText(
          textAlign: TextAlign.left,
          text: TextSpan(children: [
            WidgetSpan(
              alignment: PlaceholderAlignment.middle,
              child: Visibility(
                visible: detailData?.chainFlag == true,
                child: Container(
                  padding: EdgeInsets.only(right: 5),
                  child: Image.asset(
                    "assets/images/customer/customer_private_detail_chain.png",
                    width: 30,
                    height: 14,
                  ),
                ),
              ),
            ),
            WidgetSpan(
              alignment: PlaceholderAlignment.middle,
              child: Visibility(
                visible: detailData?.saasUserFlag == 1,
                child: Container(
                  padding: EdgeInsets.only(right: 5),
                  child: Image.asset(
                    "assets/images/customer/customer_saas.png",
                    width: 30,
                    height: 14,
                  ),
                ),
              ),
            ),
            TextSpan(
                text: detailData?.customerName?.toString() ?? "--",
                style: TextStyle(
                    fontSize: 16,
                    color: const Color(0xff333333),
                    fontWeight: FontWeight.w500)),
            WidgetSpan(
              alignment: PlaceholderAlignment.middle,
              child: Container(
                padding: const EdgeInsets.only(left: 5),
                child: Image.asset(
                  "assets/images/customer/customer_private_detail_copy.png",
                  width: 10,
                  height: 10,
                ),
              ),
            ),
          ]),
        ),
      ),
    );
  }

  /// 采购和拜访
  Widget buildPurchaseAndVisitWidget() {
    String tipsText = "";
    if (detailData?.lastPurchaseDays != null) {
      tipsText = "距离上次采购已经${detailData?.lastPurchaseDays ?? "--"}天了";
    }
    if (detailData?.overLastVisitDays != null) {
      if (tipsText.isNotEmpty) {
        tipsText += "，";
      }
      tipsText += "已经${detailData?.overLastVisitDays ?? "--"}天没拜访了";
    }
    if (tipsText.isNotEmpty) {
      tipsText = "＊ $tipsText";
    }

    return Visibility(
      visible: tipsText.isNotEmpty,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(10.5),
        color: const Color(0xfffff7ef),
        child: Text(
          tipsText,
          style: TextStyle(
              color: const Color(0xffff2121),
              fontSize: 12,
              fontWeight: FontWeight.normal),
        ),
      ),
    );
  }
}
