// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customer_detail_data_v2.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CustomerDetailDataV2 _$CustomerDetailDataV2FromJson(Map<String, dynamic> json) {
  return CustomerDetailDataV2()
    ..id = json['id']
    ..customerName = json['customerName']
    ..merchantId = json['merchantId']
    ..address = json['address']
    ..combName = json['combName']
    ..chainFlag = json['chainFlag']
    ..invoiceTypeName = json['invoiceTypeName']
    ..licenseStatusName = json['licenseStatusName']
    ..lastPurchaseDays = json['lastPurchaseDays']
    ..overLastVisitDays = json['overLastVisitDays']
    ..contactList = (json['contactList'] as List<dynamic>?)
        ?.map(
            (e) => YBMCustomerContactModel.fromJson(e as Map<String, dynamic>))
        .toList()
    ..isRegister = json['registerFlag']
    ..poiId = json['poiId']
    ..unbindFlag = json['unbindFlag']
    ..ecCustomerName = json['ecCustomerName']
    ..allocationFlag = json['allocationFlag']
    ..bindSkuCollect = (json['bindSkuCollect'] as List<dynamic>?)
        ?.map(
            (e) => CustomerSkuCollectData.fromJson(e as Map<String, dynamic>?))
        .toList()
    ..poiLongitude = json['poiLongitude']
    ..poiLatitude = json['poiLatitude']
    ..poiMobilePhone = json['poiMobilePhone']
    ..poiAuditStatus = json['poiAuditStatus']
    ..saasUserFlag = json['saasUserFlag']
    ..crmCustomerLevel = json['crmCustomerLevel'] == null
        ? null
        : CustomerLevel.fromJson(
            json['crmCustomerLevel'] as Map<String, dynamic>);
}

Map<String, dynamic> _$CustomerDetailDataV2ToJson(
        CustomerDetailDataV2 instance) =>
    <String, dynamic>{
      'id': instance.id,
      'customerName': instance.customerName,
      'merchantId': instance.merchantId,
      'address': instance.address,
      'combName': instance.combName,
      'chainFlag': instance.chainFlag,
      'invoiceTypeName': instance.invoiceTypeName,
      'licenseStatusName': instance.licenseStatusName,
      'lastPurchaseDays': instance.lastPurchaseDays,
      'overLastVisitDays': instance.overLastVisitDays,
      'contactList': instance.contactList,
      'registerFlag': instance.isRegister,
      'poiId': instance.poiId,
      'unbindFlag': instance.unbindFlag,
      'ecCustomerName': instance.ecCustomerName,
      'allocationFlag': instance.allocationFlag,
      'bindSkuCollect': instance.bindSkuCollect,
      'poiLongitude': instance.poiLongitude,
      'poiLatitude': instance.poiLatitude,
      'poiMobilePhone': instance.poiMobilePhone,
      'poiAuditStatus': instance.poiAuditStatus,
      'saasUserFlag': instance.saasUserFlag,
      'crmCustomerLevel': instance.crmCustomerLevel,
    };

CustomerLevel _$CustomerLevelFromJson(Map<String, dynamic> json) {
  return CustomerLevel()
    ..customerId = json['customerId']
    ..merchantId = json['merchantId']
    ..tips = json['tips']
    ..lastMonthLevel = json['lastMonthLevel']
    ..lastMonthLevelName = json['lastMonthLevelName']
    ..currentLevel = json['currentLevel']
    ..currentLevelName = json['currentLevelName']
    ..currentLevelTips = json['currentLevelTips']
    ..purchaseHistory = json['purchaseHistory']
    ..thisDifference = json['thisDifference']
    ..rulePay = json['rulePay'];
}

Map<String, dynamic> _$CustomerLevelToJson(CustomerLevel instance) =>
    <String, dynamic>{
      'customerId': instance.customerId,
      'merchantId': instance.merchantId,
      'tips': instance.tips,
      'lastMonthLevel': instance.lastMonthLevel,
      'lastMonthLevelName': instance.lastMonthLevelName,
      'currentLevel': instance.currentLevel,
      'currentLevelName': instance.currentLevelName,
      'currentLevelTips': instance.currentLevelTips,
      'purchaseHistory': instance.purchaseHistory,
      'thisDifference': instance.thisDifference,
      'rulePay': instance.rulePay,
    };
