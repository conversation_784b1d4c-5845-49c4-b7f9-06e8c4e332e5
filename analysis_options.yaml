analyzer:
  enable-experiment:
    - non-nullable
  strong-mode:
    implicit-casts: true
    implicit-dynamic: true
  errors:
    # Disable null safety errors for compatibility with Flutter 2.0.2
    invalid_null_aware_operator: ignore
    unnecessary_null_aware_operator_on_extension_on_nullable: ignore
    null_aware_before_operator: ignore
    
linter:
  rules:
    # Disable null safety related lints
    prefer_null_aware_operators: false
    unnecessary_null_aware_assignments: false
